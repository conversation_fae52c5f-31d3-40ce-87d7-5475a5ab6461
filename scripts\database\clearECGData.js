/**
 * Clear ECG Data Script
 * Removes all ECG-related nodes and relationships to prepare for aggregated import
 */

import dotenv from 'dotenv';
import neo4j from 'neo4j-driver';

dotenv.config();

const NEO4J_URI = process.env.VITE_NEO4J_URI;
const NEO4J_USERNAME = process.env.VITE_NEO4J_USERNAME;
const NEO4J_PASSWORD = process.env.VITE_NEO4J_PASSWORD;
const NEO4J_DATABASE = process.env.VITE_NEO4J_DATABASE || 'neo4j';

if (!NEO4J_URI || !NEO4J_USERNAME || !NEO4J_PASSWORD) {
  console.error('❌ Missing Neo4j environment variables');
  process.exit(1);
}

const driver = neo4j.driver(
  NEO4J_URI,
  neo4j.auth.basic(NEO4J_USERNAME, NEO4J_PASSWORD),
  { disableLosslessIntegers: true }
);

async function clearECGData() {
  const session = driver.session({ database: NEO4J_DATABASE });

  try {
    console.log('🧹 Starting ECG data cleanup...');

    // Get counts before deletion
    console.log('📊 Current ECG data counts:');
    const countQueries = [
      { label: 'ECGReading', query: 'MATCH (n:ECGReading) RETURN count(n) as count' },
      { label: 'ECGFeatures', query: 'MATCH (n:ECGFeatures) RETURN count(n) as count' },
      { label: 'ECGLead', query: 'MATCH (n:ECGLead) RETURN count(n) as count' },
      { label: 'D1NAMOReading', query: 'MATCH (n:D1NAMOReading) RETURN count(n) as count' }
    ];

    for (const { label, query } of countQueries) {
      try {
        const result = await session.run(query);
        const countValue = result.records[0].get('count');
        const count = neo4j.isInt(countValue) ? countValue.toNumber() : parseInt(countValue);
        console.log(`   ${label}: ${count} nodes`);
      } catch (error) {
        console.log(`   ${label}: Error getting count - ${error.message}`);
      }
    }

    console.log('\n🗑️ Deleting ECG-related nodes...');

    // Delete in batches to avoid memory issues
    const deleteQueries = [
      {
        name: 'ECG Relationships',
        query: `
          MATCH ()-[r:HAD_ECG|HAS_FEATURES|HAS_LEAD]->()
          WITH r LIMIT 10000
          DELETE r
          RETURN count(r) as deleted
        `
      },
      {
        name: 'ECGLead nodes',
        query: `
          MATCH (n:ECGLead)
          WITH n LIMIT 10000
          DETACH DELETE n
          RETURN count(n) as deleted
        `
      },
      {
        name: 'ECGFeatures nodes',
        query: `
          MATCH (n:ECGFeatures)
          WITH n LIMIT 10000
          DETACH DELETE n
          RETURN count(n) as deleted
        `
      },
      {
        name: 'ECGReading nodes',
        query: `
          MATCH (n:ECGReading)
          WITH n LIMIT 10000
          DETACH DELETE n
          RETURN count(n) as deleted
        `
      },
      {
        name: 'D1NAMOReading nodes',
        query: `
          MATCH (n:D1NAMOReading)
          WITH n LIMIT 10000
          DETACH DELETE n
          RETURN count(n) as deleted
        `
      }
    ];

    for (const { name, query } of deleteQueries) {
      console.log(`   Deleting ${name}...`);
      let totalDeleted = 0;
      let batchDeleted = 0;

      do {
        const result = await session.run(query);
        const deletedValue = result.records[0].get('deleted');
        batchDeleted = neo4j.isInt(deletedValue) ? deletedValue.toNumber() : parseInt(deletedValue);
        totalDeleted += batchDeleted;

        if (batchDeleted > 0) {
          console.log(`     Deleted ${batchDeleted} items (total: ${totalDeleted})`);
        }
      } while (batchDeleted > 0);

      console.log(`   ✅ ${name}: ${totalDeleted} total deleted`);
    }

    // Verify cleanup
    console.log('\n📊 Verifying cleanup:');
    for (const { label, query } of countQueries) {
      try {
        const result = await session.run(query);
        const countValue = result.records[0].get('count');
        const count = neo4j.isInt(countValue) ? countValue.toNumber() : parseInt(countValue);
        console.log(`   ${label}: ${count} nodes remaining`);
      } catch (error) {
        console.log(`   ${label}: Error getting count - ${error.message}`);
      }
    }

    console.log('\n✅ ECG data cleanup completed successfully!');

  } catch (error) {
    console.error('❌ Error during cleanup:', error);
    throw error;
  } finally {
    await session.close();
  }
}

// Run the cleanup
async function main() {
  try {
    await clearECGData();
  } catch (error) {
    console.error('❌ Cleanup failed:', error);
    process.exit(1);
  } finally {
    await driver.close();
  }
}

main();
