/**
 * Optimized Neo4j proxy endpoint for glucose data and healthcare queries
 * POST /api/neo4j { query: string, parameters?: object, database?: string }
 * Returns: { records: Array<object>, summary: {...} }
 *
 * Features:
 * - Connection pooling and retry logic
 * - Query optimization for glucose data
 * - Enhanced error handling and monitoring
 * - Performance metrics and caching
 */

import neo4j from 'neo4j-driver';

// CORS headers for browser requests
const CORS_HEADERS = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  'Content-Type': 'application/json'
};

// Connection pool and performance tracking
let driverInstance = null;
let connectionMetrics = {
  totalQueries: 0,
  successfulQueries: 0,
  failedQueries: 0,
  avgResponseTime: 0,
  lastHealthCheck: null
};

// Rate limiting would be configured in production
// const _rateLimiter = new Map();

// Allowed read-only query patterns (security)
const ALLOWED_QUERY_PATTERNS = [
  /^MATCH\s+.*RETURN\s+/i,
  /^OPTIONAL\s+MATCH\s+.*RETURN\s+/i,
  /^WITH\s+.*MATCH\s+.*RETURN\s+/i,
  /^UNWIND\s+.*MATCH\s+.*RETURN\s+/i
];

// Forbidden query patterns (security)
const FORBIDDEN_PATTERNS = [
  /\b(CREATE|MERGE|DELETE|SET|REMOVE|DROP|DETACH\s+DELETE)\b/i,
  /\bCALL\s+(db\.|dbms\.|apoc\.)/i,
  /\b(LOAD\s+CSV|USING\s+PERIODIC|PROFILE|EXPLAIN)\b/i
];

export async function onRequest(context) {
  try {
    const { request, env } = context;
    console.log('Neo4j API called with method:', request.method);

    if (request.method !== 'POST') {
      return new Response('Method Not Allowed', { status: 405 });
    }

    const body = await request.json().catch(() => null);
    if (!body || typeof body.query !== 'string') {
      return new Response(JSON.stringify({ error: 'Invalid payload: expected { query, parameters? }' }), {
        status: 400,
        headers: { 'content-type': 'application/json' }
      });
    }

    const cypher = body.query.trim();
    const parameters = (body.parameters && typeof body.parameters === 'object') ? body.parameters : {};

    console.log('🔧 API received parameters:', JSON.stringify(parameters, null, 2));
    const db = body.database || env.NEO4J_DATABASE || env.VITE_NEO4J_DATABASE || 'neo4j';

    // Enforce read-only: block writes/admin
    const forbidden = /(\bCREATE\b|\bMERGE\b|\bDELETE\b|DETACH\s+DELETE|\bSET\b|\bREMOVE\b|CALL\s+db\.|\bUSE\b|LOAD\s+CSV|\bAPOC\.(?:(?!(toJSON|convert|map|coll|text|math|date|temporal)).)*)/i;
    if (forbidden.test(cypher)) {
      return new Response(JSON.stringify({ error: 'Write/administrative Cypher is not allowed via API' }), {
        status: 400,
        headers: { 'content-type': 'application/json' }
      });
    }

    // Pull credentials from env (prefer non-VITE keys server-side)
    const uri = env.NEO4J_URI || env.VITE_NEO4J_URI;
    const username = env.NEO4J_USERNAME || env.VITE_NEO4J_USERNAME;
    const password = env.NEO4J_PASSWORD || env.VITE_NEO4J_PASSWORD;

    if (!uri || !username || !password) {
      return new Response(JSON.stringify({ error: 'Neo4j credentials not configured on server' }), {
        status: 500,
        headers: { 'content-type': 'application/json' }
      });
    }

    // Dynamically import driver (web/worker compatible)
    const mod = await import('neo4j-driver');
    const neo4j = mod?.default || mod;

    // Normalize scheme: if secure schemes provided, keep as-is; otherwise assume bolt/neo4j
    let driverUri = uri;

    const auth = neo4j.auth.basic(username, password);
    const driver = neo4j.driver(driverUri, auth, {});

    const session = driver.session({ database: db });
    try {
      // Sanitize limit parameters before executing query
      const sanitizedParameters = { ...parameters };
      for (const [key, value] of Object.entries(parameters)) {
        if (key === 'limit' || key.toLowerCase().includes('limit')) {
          let intValue;

          console.log(`🔧 API (simple path) processing limit parameter ${key}: ${value} (type: ${typeof value})`);

          if (Number.isInteger(value)) {
            intValue = value;
          } else if (typeof value === 'number') {
            intValue = Math.round(value);
            console.log(`🔧 API (simple path) converted float ${value} to integer ${intValue}`);
          } else {
            intValue = parseInt(String(value), 10);
          }

          // Ensure reasonable bounds
          intValue = isNaN(intValue) || intValue < 1 ? 1000 : Math.min(intValue, 50000);

          // Convert to Neo4j Integer to ensure proper type handling
          sanitizedParameters[key] = neo4j.int(intValue);

          console.log(`🔧 API (simple path) final sanitized ${key}: ${intValue} (Neo4j Integer)`);
        }
      }

      console.log('🔧 API (simple path) final parameters before query:', sanitizedParameters);
      const result = await session.run(cypher, sanitizedParameters);
      const records = result.records.map(record => {
        const obj = {};
        record.keys.forEach((key) => {
          obj[key] = serializeValue(record.get(key), neo4j);
        });
        return obj;
      });

      const response = {
        records,
        summary: {
          queryType: result.summary?.queryType,
          counters: result.summary?.counters,
          resultAvailableAfter: result.summary?.resultAvailableAfter?.toNumber?.() ?? null,
          resultConsumedAfter: result.summary?.resultConsumedAfter?.toNumber?.() ?? null
        }
      };

      return new Response(JSON.stringify(response), {
        headers: { 'content-type': 'application/json' }
      });
    } finally {
      await session.close();
      await driver.close();
    }
  } catch (err) {
    console.error('Neo4j API Error:', err);
    console.error('Error details:', {
      message: err?.message,
      code: err?.code,
      stack: err?.stack?.substring(0, 500)
    });

    return new Response(JSON.stringify({
      error: String(err?.message || err),
      code: err?.code,
      details: 'Check server logs for more information'
    }), {
      status: 500,
      headers: { 'content-type': 'application/json' }
    });
  }
}

function serializeValue(value, neo4j) {
  if (value == null) return null;

  if (neo4j.isInt?.(value)) return value.toNumber();
  if (neo4j.isDateTime?.(value) || neo4j.isDate?.(value) || neo4j.isTime?.(value)) return value.toString();

  // Node
  if (value && typeof value === 'object' && value.labels !== undefined) {
    return {
      id: neo4j.isInt?.(value.identity) ? value.identity.toNumber() : value.identity,
      labels: value.labels,
      properties: mapObject(value.properties, (v) => serializeValue(v, neo4j))
    };
  }
  // Relationship
  if (value && typeof value === 'object' && value.type !== undefined && value.start !== undefined) {
    return {
      id: neo4j.isInt?.(value.identity) ? value.identity.toNumber() : value.identity,
      type: value.type,
      start: neo4j.isInt?.(value.start) ? value.start.toNumber() : value.start,
      end: neo4j.isInt?.(value.end) ? value.end.toNumber() : value.end,
      properties: mapObject(value.properties, (v) => serializeValue(v, neo4j))
    };
  }
  // Path
  if (value && typeof value === 'object' && value.segments !== undefined) {
    return {
      start: serializeValue(value.start, neo4j),
      end: serializeValue(value.end, neo4j),
      segments: value.segments.map(s => ({
        start: serializeValue(s.start, neo4j),
        relationship: serializeValue(s.relationship, neo4j),
        end: serializeValue(s.end, neo4j)
      })),
      length: value.length
    };
  }
  if (Array.isArray(value)) return value.map(v => serializeValue(v, neo4j));
  if (typeof value === 'object') return mapObject(value, (v) => serializeValue(v, neo4j));
  return value;
}

function mapObject(obj, mapFn) {
  const out = {};
  for (const [k, v] of Object.entries(obj || {})) out[k] = mapFn(v);
  return out;
}

async function _handleConnect(env) {
  if (!env.NEO4J_PASSWORD) {
    return errorResponse('Neo4j credentials not configured. Please set NEO4J_PASSWORD environment variable.', 503);
  }

  let driver = null;
  try {
    const config = {
      uri: env.NEO4J_URI || 'bolt://localhost:7687',
      username: env.NEO4J_USERNAME || 'neo4j',
      password: env.NEO4J_PASSWORD,
      database: env.NEO4J_DATABASE || 'neo4j'
    };

    driver = neo4j.driver(
      config.uri,
      neo4j.auth.basic(config.username, config.password),
      {
        maxConnectionPoolSize: 5,
        connectionAcquisitionTimeout: 10000,
        maxTransactionRetryTime: 10000
      }
    );

    const session = driver.session({ database: config.database });

    try {
      const result = await session.run('RETURN 1 as test, datetime() as timestamp');
      const timestamp = result.records[0].get('timestamp').toString();

      return successResponse({
        success: true,
        message: 'Successfully connected to Neo4j database',
        timestamp: timestamp,
        mockMode: false
      });
    } finally {
      await session.close();
    }
  } catch (error) {
    console.error('Neo4j connection error:', error);
    return errorResponse(`Failed to connect to Neo4j database: ${error.message}`, 503);
  } finally {
    if (driver) {
      try {
        await driver.close();
      } catch (closeError) {
        console.error('Error closing driver:', closeError);
      }
    }
  }
}

async function _handleQuery(env, query, parameters) {
  if (!query || typeof query !== 'string') {
    return errorResponse('Query is required', 400);
  }

  // Security validation
  const queryTrimmed = query.trim();

  // Check for forbidden patterns
  for (const pattern of FORBIDDEN_PATTERNS) {
    if (pattern.test(queryTrimmed)) {
      return errorResponse('Query contains forbidden operations. Only read-only queries are allowed.', 403);
    }
  }

  // Check for allowed patterns
  const isAllowed = ALLOWED_QUERY_PATTERNS.some(pattern => pattern.test(queryTrimmed));
  if (!isAllowed) {
    return errorResponse('Query must start with MATCH, OPTIONAL MATCH, WITH, or UNWIND and contain RETURN clause.', 403);
  }

  // Require Neo4j credentials
  if (!env.NEO4J_PASSWORD) {
    return errorResponse('Neo4j credentials not configured. Please set NEO4J_PASSWORD environment variable.', 503);
  }

  // Execute real query
  let driver = null;
  try {
    const config = {
      uri: env.NEO4J_URI || 'bolt://localhost:7687',
      username: env.NEO4J_USERNAME || 'neo4j',
      password: env.NEO4J_PASSWORD,
      database: env.NEO4J_DATABASE || 'neo4j'
    };

    driver = neo4j.driver(
      config.uri,
      neo4j.auth.basic(config.username, config.password),
      { maxConnectionPoolSize: 5 }
    );

    const session = driver.session({ database: config.database });

    try {
      const startTime = Date.now();

      // Add safety limits to parameters with robust type handling
      const safeParameters = { ...parameters };

      // Handle days parameter
      if (parameters.days !== undefined) {
        const days = typeof parameters.days === 'number' ? Math.round(parameters.days) : parseInt(parameters.days, 10);
        safeParameters.days = Math.min(Math.max(1, days || 30), 365);
      }

      // Handle limit parameters with robust float handling
      for (const [key, value] of Object.entries(parameters)) {
        if (key === 'limit' || key.toLowerCase().includes('limit')) {
          let intValue;

          console.log(`🔧 API processing limit parameter ${key}: ${value} (type: ${typeof value})`);

          if (Number.isInteger(value)) {
            intValue = value;
          } else if (typeof value === 'number') {
            intValue = Math.round(value);
            console.log(`🔧 API converted float ${value} to integer ${intValue}`);
          } else {
            intValue = parseInt(String(value), 10);
          }

          // Ensure reasonable bounds
          intValue = isNaN(intValue) || intValue < 1 ? 1000 : Math.min(intValue, 50000);

          // Convert to Neo4j Integer to ensure proper type handling
          safeParameters[key] = neo4j.int(intValue);

          console.log(`🔧 API final sanitized ${key}: ${intValue} (Neo4j Integer)`);
        }
      }

      console.log('🔧 API final parameters before query:', safeParameters);
      const result = await session.run(query, safeParameters);
      const executionTime = Date.now() - startTime;

      const processedResults = result.records.map(record => {
        const obj = {};
        record.keys.forEach(key => {
          obj[key] = convertNeo4jValue(record.get(key));
        });
        return obj;
      });

      return successResponse({
        results: processedResults,
        mockMode: false,
        executionTime: executionTime,
        recordCount: processedResults.length
      });
    } finally {
      await session.close();
    }
  } catch (error) {
    console.error('Query execution error:', error);
    return errorResponse(`Query execution failed: ${error.message}`, 500);
  } finally {
    if (driver) {
      try {
        await driver.close();
      } catch (closeError) {
        console.error('Error closing driver:', closeError);
      }
    }
  }
}

async function _handleDisconnect() {
  return successResponse({
    success: true,
    message: 'Disconnected from Neo4j',
    timestamp: new Date().toISOString()
  });
}

function convertNeo4jValue(value) {
  if (value === null || value === undefined) {
    return null;
  }

  if (neo4j.isInt(value)) {
    return value.toNumber();
  }

  if (neo4j.isDateTime(value) || neo4j.isDate(value) || neo4j.isTime(value)) {
    return value.toString();
  }

  if (value.labels !== undefined) { // Node
    return {
      id: neo4j.isInt(value.identity) ? value.identity.toNumber() : value.identity,
      labels: value.labels,
      properties: Object.fromEntries(
        Object.entries(value.properties).map(([k, v]) => [k, convertNeo4jValue(v)])
      )
    };
  }

  if (value.type !== undefined && value.start !== undefined) { // Relationship
    return {
      id: neo4j.isInt(value.identity) ? value.identity.toNumber() : value.identity,
      type: value.type,
      start: neo4j.isInt(value.start) ? value.start.toNumber() : value.start,
      end: neo4j.isInt(value.end) ? value.end.toNumber() : value.end,
      properties: Object.fromEntries(
        Object.entries(value.properties).map(([k, v]) => [k, convertNeo4jValue(v)])
      )
    };
  }

  if (Array.isArray(value)) {
    return value.map(item => convertNeo4jValue(item));
  }

  if (typeof value === 'object' && value !== null) {
    const result = {};
    for (const [key, val] of Object.entries(value)) {
      result[key] = convertNeo4jValue(val);
    }
    return result;
  }

  return value;
}

function successResponse(data) {
  return new Response(JSON.stringify(data), {
    status: 200,
    headers: CORS_HEADERS
  });
}

function errorResponse(message, status = 400) {
  return new Response(JSON.stringify({
    error: true,
    message: message,
    timestamp: new Date().toISOString()
  }), {
    status: status,
    headers: CORS_HEADERS
  });
}
