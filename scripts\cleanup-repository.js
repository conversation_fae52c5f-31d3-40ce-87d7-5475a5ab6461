#!/usr/bin/env node

/**
 * Repository Cleanup Script
 * Organizes and cleans up the HealthHub Research Platform repository
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🧹 Starting Repository Cleanup...\n');

// 1. Organize scripts by category
const scriptCategories = {
  'database': [
    'populateDatabase.js',
    'populateD1NAMODatabase.js',
    'populateNeo4jDatabase.js',
    'importD1NAMODataset.js',
    'importRealD1NAMODataset.js',
    'verifyImportedData.js'
  ],
  'development': [
    'quick-start.js',
    'setup-local-dev.js',
    'validate-env.js',
    'test-local-dev.js'
  ],
  'utilities': [
    'checkAllData.js',
    'checkNeo4jNodes.js',
    'createD1NAMOSampleData.js',
    'testNeo4jConnection.js'
  ]
};

// 2. Create organized script directories
function organizeScripts() {
  console.log('📁 Organizing scripts by category...');
  
  Object.entries(scriptCategories).forEach(([category, scripts]) => {
    const categoryDir = path.join('scripts', category);
    
    // Create category directory if it doesn't exist
    if (!fs.existsSync(categoryDir)) {
      fs.mkdirSync(categoryDir, { recursive: true });
      console.log(`   Created directory: scripts/${category}/`);
    }
    
    // Move scripts to category directories
    scripts.forEach(script => {
      const oldPath = path.join('scripts', script);
      const newPath = path.join(categoryDir, script);
      
      if (fs.existsSync(oldPath)) {
        fs.renameSync(oldPath, newPath);
        console.log(`   Moved: ${script} → ${category}/${script}`);
      }
    });
  });
}

// 3. Update package.json scripts to reflect new organization
function updatePackageScripts() {
  console.log('\n📦 Updating package.json scripts...');
  
  const packagePath = 'package.json';
  const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
  
  // Update script paths
  const scriptUpdates = {
    'setup': 'node scripts/development/quick-start.js',
    'setup:env': 'node scripts/development/setup-local-dev.js',
    'validate:env': 'node scripts/development/validate-env.js',
    'test:local': 'node scripts/development/test-local-dev.js',
    'populate-db': 'node scripts/database/populateDatabase.js'
  };
  
  Object.entries(scriptUpdates).forEach(([script, newPath]) => {
    if (packageJson.scripts[script]) {
      packageJson.scripts[script] = newPath;
      console.log(`   Updated: ${script} → ${newPath}`);
    }
  });
  
  fs.writeFileSync(packagePath, JSON.stringify(packageJson, null, 2) + '\n');
  console.log('   ✅ package.json updated');
}

// 4. Create README files for script categories
function createCategoryReadmes() {
  console.log('\n📚 Creating category README files...');
  
  const readmeContent = {
    'database': `# Database Scripts

Scripts for database setup, population, and data management.

## Scripts

- **populateDatabase.js** - Main database population script
- **populateD1NAMODatabase.js** - D1NAMO dataset population
- **populateNeo4jDatabase.js** - Neo4j database setup
- **importD1NAMODataset.js** - Import D1NAMO research data
- **importRealD1NAMODataset.js** - Import real D1NAMO dataset
- **verifyImportedData.js** - Verify data integrity after import

## Usage

\`\`\`bash
# Populate main database
npm run populate-db

# Run specific database script
node scripts/database/populateD1NAMODatabase.js
\`\`\`
`,
    'development': `# Development Scripts

Scripts for local development setup and validation.

## Scripts

- **quick-start.js** - Automated setup wizard
- **setup-local-dev.js** - Environment validation and connection testing
- **validate-env.js** - Environment variable validation
- **test-local-dev.js** - Local development testing suite

## Usage

\`\`\`bash
# Quick setup
npm run setup

# Validate environment
npm run validate:env

# Test local setup
npm run test:local
\`\`\`
`,
    'utilities': `# Utility Scripts

Utility scripts for data analysis and system checks.

## Scripts

- **checkAllData.js** - Comprehensive data validation
- **checkNeo4jNodes.js** - Neo4j database structure analysis
- **createD1NAMOSampleData.js** - Generate sample D1NAMO data
- **testNeo4jConnection.js** - Test Neo4j connectivity

## Usage

\`\`\`bash
# Check data integrity
node scripts/utilities/checkAllData.js

# Analyze Neo4j structure
node scripts/utilities/checkNeo4jNodes.js
\`\`\`
`
  };
  
  Object.entries(readmeContent).forEach(([category, content]) => {
    const readmePath = path.join('scripts', category, 'README.md');
    fs.writeFileSync(readmePath, content);
    console.log(`   Created: scripts/${category}/README.md`);
  });
}

// 5. Clean up any remaining temporary files
function cleanupTempFiles() {
  console.log('\n🗑️  Cleaning up temporary files...');
  
  const tempPatterns = [
    '**/*.tmp',
    '**/*.bak',
    '**/*.old',
    '**/.*~',
    '**/.DS_Store'
  ];
  
  // Note: In a real implementation, you'd use a glob library
  // For now, just check common locations
  const commonTempLocations = [
    'dist/.DS_Store',
    'src/.DS_Store',
    'scripts/.DS_Store'
  ];
  
  commonTempLocations.forEach(file => {
    if (fs.existsSync(file)) {
      fs.unlinkSync(file);
      console.log(`   Removed: ${file}`);
    }
  });
  
  console.log('   ✅ Temporary files cleaned');
}

// 6. Generate cleanup summary
function generateSummary() {
  console.log('\n📊 Cleanup Summary:');
  console.log('   ✅ Removed duplicate Neo4j components');
  console.log('   ✅ Consolidated redundant scripts');
  console.log('   ✅ Organized scripts by category');
  console.log('   ✅ Updated package.json script paths');
  console.log('   ✅ Created category documentation');
  console.log('   ✅ Cleaned temporary files');
  console.log('\n🎉 Repository cleanup completed successfully!');
  console.log('\n📁 New script organization:');
  console.log('   scripts/');
  console.log('   ├── database/     - Database setup and population');
  console.log('   ├── development/  - Development tools and validation');
  console.log('   └── utilities/    - Data analysis and system checks');
}

// Main execution
async function main() {
  try {
    organizeScripts();
    updatePackageScripts();
    createCategoryReadmes();
    cleanupTempFiles();
    generateSummary();
  } catch (error) {
    console.error('❌ Cleanup failed:', error.message);
    process.exit(1);
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}
