/**
 * Glucose Service Performance Monitor
 * Displays real-time metrics for the glucose data service
 */

import { useState, useEffect } from 'react';
import glucoseDataService from '../services/glucoseDataService';
import './GlucoseServiceMonitor.css';

function GlucoseServiceMonitor({ compact = false }) {
  const [metrics, setMetrics] = useState(null);
  const [health, setHealth] = useState(null);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const updateMetrics = () => {
      const serviceMetrics = glucoseDataService.getMetrics();
      setMetrics(serviceMetrics);
    };

    const checkHealth = async () => {
      try {
        const healthStatus = await glucoseDataService.healthCheck();
        setHealth(healthStatus);
      } catch (error) {
        setHealth({
          healthy: false,
          error: error.message,
          service: 'GlucoseDataService'
        });
      }
    };

    // Initial load
    updateMetrics();
    checkHealth();

    // Update metrics every 30 seconds
    const metricsInterval = setInterval(updateMetrics, 30000);
    
    // Health check every 2 minutes
    const healthInterval = setInterval(checkHealth, 120000);

    return () => {
      clearInterval(metricsInterval);
      clearInterval(healthInterval);
    };
  }, []);

  const formatNumber = (num) => {
    if (num === undefined || num === null) return 'N/A';
    return typeof num === 'number' ? num.toLocaleString() : num;
  };

  const formatTime = (ms) => {
    if (!ms) return 'N/A';
    return `${Math.round(ms)}ms`;
  };

  const getHealthColor = () => {
    if (!health) return '#gray';
    return health.healthy ? '#4CAF50' : '#f44336';
  };

  const getPerformanceColor = (value, threshold = 1000) => {
    if (!value) return '#gray';
    return value < threshold ? '#4CAF50' : value < threshold * 2 ? '#FF9800' : '#f44336';
  };

  if (compact) {
    return (
      <div className="glucose-monitor-compact">
        <button 
          className="monitor-toggle"
          onClick={() => setIsVisible(!isVisible)}
          style={{ backgroundColor: getHealthColor() }}
        >
          📊 Glucose Service
        </button>
        
        {isVisible && (
          <div className="monitor-popup">
            <div className="monitor-header">
              <h4>Glucose Data Service</h4>
              <button onClick={() => setIsVisible(false)}>×</button>
            </div>
            
            <div className="monitor-content">
              {health && (
                <div className="health-status">
                  <span className={`status-indicator ${health.healthy ? 'healthy' : 'unhealthy'}`}>
                    {health.healthy ? '✅' : '❌'}
                  </span>
                  <span>{health.healthy ? 'Healthy' : 'Unhealthy'}</span>
                </div>
              )}
              
              {metrics && (
                <div className="metrics-summary">
                  <div className="metric">
                    <span>Requests:</span>
                    <span>{formatNumber(metrics.totalRequests)}</span>
                  </div>
                  <div className="metric">
                    <span>Cache Hit Rate:</span>
                    <span>{metrics.cacheHitRate}</span>
                  </div>
                  <div className="metric">
                    <span>Avg Response:</span>
                    <span style={{ color: getPerformanceColor(metrics.avgResponseTime) }}>
                      {formatTime(metrics.avgResponseTime)}
                    </span>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="glucose-service-monitor">
      <div className="monitor-header">
        <h3>🩺 Glucose Data Service Monitor</h3>
        <div className="header-actions">
          <button 
            onClick={() => glucoseDataService.clearCache()}
            className="clear-cache-btn"
          >
            🧹 Clear Cache
          </button>
        </div>
      </div>

      <div className="monitor-grid">
        {/* Health Status */}
        <div className="monitor-card health-card">
          <h4>Service Health</h4>
          {health ? (
            <div className="health-details">
              <div className={`health-indicator ${health.healthy ? 'healthy' : 'unhealthy'}`}>
                <span className="status-icon">
                  {health.healthy ? '✅' : '❌'}
                </span>
                <span className="status-text">
                  {health.healthy ? 'Healthy' : 'Unhealthy'}
                </span>
              </div>
              
              {health.error && (
                <div className="error-message">
                  <strong>Error:</strong> {health.error}
                </div>
              )}
              
              {health.backend && (
                <div className="backend-info">
                  <div>Backend: {health.backend.healthy ? '✅' : '❌'}</div>
                  <div>Response Time: {formatTime(health.backend.responseTime)}</div>
                </div>
              )}
            </div>
          ) : (
            <div className="loading">Checking health...</div>
          )}
        </div>

        {/* Performance Metrics */}
        <div className="monitor-card metrics-card">
          <h4>Performance Metrics</h4>
          {metrics ? (
            <div className="metrics-grid">
              <div className="metric-item">
                <span className="metric-label">Total Requests</span>
                <span className="metric-value">{formatNumber(metrics.totalRequests)}</span>
              </div>
              
              <div className="metric-item">
                <span className="metric-label">Cache Hits</span>
                <span className="metric-value">{formatNumber(metrics.cacheHits)}</span>
              </div>
              
              <div className="metric-item">
                <span className="metric-label">Cache Hit Rate</span>
                <span className="metric-value cache-rate">{metrics.cacheHitRate}</span>
              </div>
              
              <div className="metric-item">
                <span className="metric-label">Avg Response Time</span>
                <span 
                  className="metric-value"
                  style={{ color: getPerformanceColor(metrics.avgResponseTime) }}
                >
                  {formatTime(metrics.avgResponseTime)}
                </span>
              </div>
              
              <div className="metric-item">
                <span className="metric-label">Errors</span>
                <span className="metric-value error-count">
                  {formatNumber(metrics.errors)}
                </span>
              </div>
              
              <div className="metric-item">
                <span className="metric-label">Cache Size</span>
                <span className="metric-value">{formatNumber(metrics.cacheSize)}</span>
              </div>
            </div>
          ) : (
            <div className="loading">Loading metrics...</div>
          )}
        </div>

        {/* Quick Actions */}
        <div className="monitor-card actions-card">
          <h4>Quick Actions</h4>
          <div className="action-buttons">
            <button 
              onClick={() => glucoseDataService.clearCache()}
              className="action-btn clear-btn"
            >
              🧹 Clear Cache
            </button>
            
            <button 
              onClick={async () => {
                try {
                  const health = await glucoseDataService.healthCheck();
                  setHealth(health);
                } catch (error) {
                  setHealth({ healthy: false, error: error.message });
                }
              }}
              className="action-btn refresh-btn"
            >
              🔄 Refresh Health
            </button>
            
            <button 
              onClick={() => {
                const metrics = glucoseDataService.getMetrics();
                setMetrics(metrics);
              }}
              className="action-btn update-btn"
            >
              📊 Update Metrics
            </button>
          </div>
        </div>
      </div>

      {/* Performance Tips */}
      <div className="performance-tips">
        <h4>💡 Performance Tips</h4>
        <ul>
          <li>Cache hit rate above 50% indicates good performance</li>
          <li>Response times under 1000ms are optimal</li>
          <li>Clear cache if data seems stale</li>
          <li>High error count may indicate connectivity issues</li>
        </ul>
      </div>
    </div>
  );
}

export default GlucoseServiceMonitor;
