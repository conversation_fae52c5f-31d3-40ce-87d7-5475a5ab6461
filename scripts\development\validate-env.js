#!/usr/bin/env node

/**
 * Environment Validation Utility
 * 
 * Validates that all required environment variables are properly configured
 * for the HealthHub Research Platform
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.join(__dirname, '..');

// Environment variable definitions
const ENV_DEFINITIONS = {
  // Frontend (Vite) variables
  VITE_AI_ENABLED: {
    required: true,
    type: 'boolean',
    description: 'Enable AI-powered recommendations',
    defaultValue: 'true'
  },
  VITE_OPENAI_API_KEY: {
    required: true,
    type: 'string',
    description: 'OpenAI API key for AI features',
    validation: (value) => value.startsWith('sk-') ? null : 'Should start with "sk-"'
  },
  VITE_OPENAI_MODEL: {
    required: false,
    type: 'string',
    description: 'OpenAI model to use',
    defaultValue: 'gpt-4o-mini'
  },
  VITE_NEO4J_USE_API: {
    required: true,
    type: 'boolean',
    description: 'Use API proxy for Neo4j (recommended)',
    defaultValue: 'true'
  },
  VITE_NEO4J_URI: {
    required: true,
    type: 'string',
    description: 'Neo4j database URI',
    validation: (value) => {
      if (value.startsWith('neo4j://') || value.startsWith('neo4j+s://') || value.startsWith('bolt://')) {
        return null;
      }
      return 'Should start with neo4j://, neo4j+s://, or bolt://';
    }
  },
  VITE_NEO4J_USERNAME: {
    required: true,
    type: 'string',
    description: 'Neo4j username'
  },
  VITE_NEO4J_PASSWORD: {
    required: true,
    type: 'string',
    description: 'Neo4j password',
    sensitive: true
  },
  VITE_NEO4J_DATABASE: {
    required: false,
    type: 'string',
    description: 'Neo4j database name',
    defaultValue: 'neo4j'
  },

  // Server-side variables (for .dev.vars)
  NEO4J_URI: {
    required: true,
    type: 'string',
    description: 'Neo4j URI for server-side API',
    file: '.dev.vars'
  },
  NEO4J_USERNAME: {
    required: true,
    type: 'string',
    description: 'Neo4j username for server-side API',
    file: '.dev.vars'
  },
  NEO4J_PASSWORD: {
    required: true,
    type: 'string',
    description: 'Neo4j password for server-side API',
    file: '.dev.vars',
    sensitive: true
  },
  NEO4J_DATABASE: {
    required: false,
    type: 'string',
    description: 'Neo4j database for server-side API',
    file: '.dev.vars',
    defaultValue: 'neo4j'
  },
  OPENAI_API_KEY: {
    required: true,
    type: 'string',
    description: 'OpenAI API key for server-side API',
    file: '.dev.vars',
    sensitive: true,
    validation: (value) => value.startsWith('sk-') ? null : 'Should start with "sk-"'
  }
};

function parseEnvFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const env = {};
    
    content.split('\n').forEach(line => {
      const trimmed = line.trim();
      if (trimmed && !trimmed.startsWith('#')) {
        const equalIndex = trimmed.indexOf('=');
        if (equalIndex > 0) {
          const key = trimmed.substring(0, equalIndex).trim();
          const value = trimmed.substring(equalIndex + 1).trim();
          env[key] = value;
        }
      }
    });
    
    return env;
  } catch (error) {
    return null;
  }
}

function validateValue(key, value, definition) {
  const issues = [];
  
  if (!value || value === '') {
    if (definition.required) {
      issues.push(`Missing required variable: ${key}`);
    }
    return issues;
  }
  
  // Type validation
  if (definition.type === 'boolean') {
    if (!['true', 'false'].includes(value.toLowerCase())) {
      issues.push(`${key} should be 'true' or 'false'`);
    }
  }
  
  // Custom validation
  if (definition.validation) {
    const validationError = definition.validation(value);
    if (validationError) {
      issues.push(`${key}: ${validationError}`);
    }
  }
  
  return issues;
}

function maskSensitiveValue(value, sensitive) {
  if (!sensitive || !value) return value;
  if (value.length <= 8) return '*'.repeat(value.length);
  return value.substring(0, 4) + '*'.repeat(value.length - 8) + value.substring(value.length - 4);
}

function validateEnvironment() {
  console.log('🔍 Validating Environment Configuration\n');
  
  const envPath = path.join(rootDir, '.env');
  const devVarsPath = path.join(rootDir, '.dev.vars');
  
  const envVars = parseEnvFile(envPath);
  const devVars = parseEnvFile(devVarsPath);
  
  if (!envVars) {
    console.log('❌ .env file not found or unreadable');
    return false;
  }
  
  if (!devVars) {
    console.log('❌ .dev.vars file not found or unreadable');
    return false;
  }
  
  let allValid = true;
  const issues = [];
  
  // Validate each environment variable
  Object.entries(ENV_DEFINITIONS).forEach(([key, definition]) => {
    const isDevVar = definition.file === '.dev.vars';
    const source = isDevVar ? devVars : envVars;
    const fileName = isDevVar ? '.dev.vars' : '.env';
    
    const value = source[key];
    const validationIssues = validateValue(key, value, definition);
    
    if (validationIssues.length > 0) {
      issues.push(...validationIssues.map(issue => `[${fileName}] ${issue}`));
      allValid = false;
    } else if (value) {
      const displayValue = maskSensitiveValue(value, definition.sensitive);
      console.log(`✅ ${key} = ${displayValue} (${fileName})`);
    } else if (definition.defaultValue) {
      console.log(`⚠️  ${key} using default: ${definition.defaultValue} (${fileName})`);
    }
  });
  
  // Report issues
  if (issues.length > 0) {
    console.log('\n❌ Environment Issues Found:');
    issues.forEach(issue => console.log(`   ${issue}`));
  }
  
  // Check for consistency between .env and .dev.vars
  console.log('\n🔄 Checking Consistency...');
  const consistencyChecks = [
    { env: 'VITE_NEO4J_URI', dev: 'NEO4J_URI' },
    { env: 'VITE_NEO4J_USERNAME', dev: 'NEO4J_USERNAME' },
    { env: 'VITE_NEO4J_PASSWORD', dev: 'NEO4J_PASSWORD' },
    { env: 'VITE_OPENAI_API_KEY', dev: 'OPENAI_API_KEY' }
  ];
  
  consistencyChecks.forEach(({ env, dev }) => {
    const envValue = envVars[env];
    const devValue = devVars[dev];
    
    if (envValue && devValue) {
      if (envValue === devValue) {
        console.log(`✅ ${env} matches ${dev}`);
      } else {
        console.log(`⚠️  ${env} differs from ${dev} - this may cause issues`);
      }
    }
  });
  
  return allValid;
}

function generateEnvTemplate() {
  console.log('\n📝 Environment Variable Reference:\n');
  
  Object.entries(ENV_DEFINITIONS).forEach(([key, definition]) => {
    const fileName = definition.file || '.env';
    const required = definition.required ? '(Required)' : '(Optional)';
    const defaultVal = definition.defaultValue ? ` [Default: ${definition.defaultValue}]` : '';
    
    console.log(`${key}=${definition.defaultValue || ''}`);
    console.log(`  # ${definition.description} ${required}${defaultVal}`);
    console.log(`  # File: ${fileName}`);
    console.log('');
  });
}

function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--template')) {
    generateEnvTemplate();
    return;
  }
  
  const isValid = validateEnvironment();
  
  if (isValid) {
    console.log('\n🎉 Environment configuration is valid!');
    console.log('You can now run: npm run dev:local');
  } else {
    console.log('\n❌ Environment configuration has issues.');
    console.log('Please fix the issues above before starting development.');
    console.log('\nFor help, run: node scripts/validate-env.js --template');
    process.exit(1);
  }
}

if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}
