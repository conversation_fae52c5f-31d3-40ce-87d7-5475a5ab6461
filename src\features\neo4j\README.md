# Neo4j Feature - Complete Database Solution

This folder contains a comprehensive Neo4j database solution with advanced features for healthcare research platforms.

## 🚀 Features

### Core Components
- **Neo4jWorkbench**: Interactive query workbench for database exploration
- **QueryRunner**: Execute predefined and custom Cypher queries with results visualization
- **Neo4jHealthDashboard**: Real-time monitoring and health metrics dashboard

### Services
- **Neo4jService**: Complete database service with connection management, caching, and security
- **SchemaManager**: Database schema management, validation, and optimization
- **BackupManager**: Comprehensive backup and recovery utilities

### Utilities
- **AGP Calculation**: Ambulatory Glucose Profile calculations for diabetes research
- **AGP Reporting**: Export and visualization tools for glucose data
- **Performance Monitoring**: Query performance tracking and optimization suggestions
- **Security Validation**: Query validation and rate limiting

## 📁 Directory Structure

```
src/features/neo4j/
├── components/           # React UI components
│   ├── Neo4jWorkbench.jsx
│   ├── QueryRunner.jsx
│   ├── Neo4jHealthDashboard.jsx
│   └── *.module.css     # Component styles
├── services/            # Core services
│   └── neo4jService.js  # Main Neo4j service
├── utils/               # Utility functions
│   ├── agpCalculation.js
│   ├── agpReporting.js
│   ├── schemaManager.js
│   └── backupManager.js
├── tests/               # Test suites
│   └── neo4jService.test.js
└── README.md           # This file
```

## 🔧 Installation & Setup

### Prerequisites
- Neo4j database (local or AuraDB)
- Node.js 18+
- React 18+

### Environment Variables
```bash
# Neo4j Configuration
VITE_NEO4J_URI=neo4j+s://your-instance.databases.neo4j.io
VITE_NEO4J_USERNAME=neo4j
VITE_NEO4J_PASSWORD=your-password
VITE_NEO4J_DATABASE=neo4j
VITE_NEO4J_USE_API=true  # Use API proxy for browser security

# Server-side (for Cloudflare Functions)
NEO4J_URI=neo4j+s://your-instance.databases.neo4j.io
NEO4J_USERNAME=neo4j
NEO4J_PASSWORD=your-password
NEO4J_DATABASE=neo4j
```

### Installation
```bash
npm install neo4j-driver
```

## 🚀 Quick Start

### Basic Usage
```javascript
import { Neo4jService, Neo4jWorkbench } from '../features/neo4j';

// Initialize service
const neo4jService = new Neo4jService();

// Connect to database
await neo4jService.connect();

// Execute query
const result = await neo4jService.executeQuery(
  'MATCH (p:Patient) RETURN p LIMIT 10'
);

// Use in React component
function MyComponent() {
  return <Neo4jWorkbench />;
}
```

### Advanced Features
```javascript
import {
  Neo4jService,
  SchemaManager,
  BackupManager,
  Neo4jHealthDashboard
} from '../features/neo4j';

const service = new Neo4jService();
const schemaManager = new SchemaManager(service);
const backupManager = new BackupManager(service);

// Schema management
const schema = await schemaManager.refreshSchema();
const analysis = await schemaManager.analyzeSchema();

// Create indexes
await schemaManager.createIndexes([
  {
    name: 'patient_id_idx',
    label: 'Patient',
    properties: ['patientId'],
    type: 'BTREE'
  }
]);

// Backup database
const backup = await backupManager.createFullBackup({
  format: 'cypher',
  includeSchema: true,
  includeData: true
});

// Health monitoring
function HealthMonitoring() {
  return <Neo4jHealthDashboard />;
}
```

## 📊 Health Dashboard

The Neo4j Health Dashboard provides real-time monitoring of your database:

- **Connection Status**: Real-time health checks
- **Query Performance**: Execution times, slow queries, cache hits
- **Rate Limiting**: Request monitoring and throttling
- **Schema Overview**: Node labels, relationships, indexes
- **Error Tracking**: Failed queries and connection issues

## 🔒 Security Features

### Query Validation
- Prevents dangerous operations (DROP, DELETE ALL, etc.)
- Validates query length and complexity
- Sanitizes parameters

### Rate Limiting
- Configurable request limits per time window
- Automatic throttling for excessive requests
- Per-user rate limiting support

### API Proxy
- Secure server-side query execution
- Read-only query enforcement
- CORS protection

## 🧪 Testing

Run the comprehensive test suite:

```bash
npm test src/features/neo4j/tests/
```

Test coverage includes:
- Connection management
- Query execution
- Validation and security
- Schema management
- Backup and recovery
- Health monitoring

## 📈 Performance Optimization

### Query Caching
- Automatic caching of frequently used queries
- Configurable cache timeout
- Cache hit rate monitoring

### Connection Pooling
- Efficient connection management
- Automatic reconnection on failure
- Connection health monitoring

### Index Suggestions
- Automatic analysis of query patterns
- Performance improvement recommendations
- Index creation utilities

## 🔄 Backup & Recovery

### Full Database Backup
```javascript
const backup = await backupManager.createFullBackup({
  format: 'cypher',        // 'cypher', 'json', 'csv'
  includeSchema: true,     // Include indexes and constraints
  includeData: true,       // Include all data
  batchSize: 1000,        // Batch size for large datasets
  onProgress: (progress) => {
    console.log(`Progress: ${progress.percentage}%`);
  }
});

// Export to file
backupManager.exportBackup(backup, 'my-backup.json');
```

### Restore from Backup
```javascript
// Import from file
const backup = await backupManager.importBackup(file);

// Restore database
await backupManager.restoreFromBackup(backup, {
  clearExisting: true,     // Clear existing data
  restoreSchema: true,     // Restore indexes/constraints
  restoreData: true,       // Restore all data
  batchSize: 1000
});
```

## 🔧 Schema Management

### Define Schema
```javascript
schemaManager.defineSchema('healthcare', {
  nodes: {
    Patient: {
      properties: ['patientId', 'name', 'dateOfBirth'],
      required: ['patientId'],
      indexes: ['patientId']
    },
    Doctor: {
      properties: ['doctorId', 'name', 'specialty'],
      required: ['doctorId'],
      indexes: ['doctorId']
    }
  },
  relationships: {
    TREATS: {
      from: 'Doctor',
      to: 'Patient',
      properties: ['since']
    }
  }
});
```

### Create Indexes and Constraints
```javascript
// Create indexes
await schemaManager.createIndexes([
  {
    name: 'patient_id_idx',
    label: 'Patient',
    properties: ['patientId'],
    type: 'BTREE'
  },
  {
    name: 'glucose_timestamp_idx',
    label: 'GlucoseReading',
    properties: ['timestamp'],
    type: 'BTREE'
  }
]);

// Create constraints
await schemaManager.createConstraints([
  {
    name: 'patient_id_unique',
    label: 'Patient',
    properties: ['patientId'],
    type: 'UNIQUE'
  }
]);
```

### Schema Analysis
```javascript
const analysis = await schemaManager.analyzeSchema();

console.log('Suggestions:', analysis.suggestions);
console.log('Warnings:', analysis.warnings);
console.log('Statistics:', analysis.statistics);
```

## 📊 Data Export

### Export Query Results
```javascript
// Export as JSON
const jsonData = await service.exportData(
  'MATCH (p:Patient) RETURN p',
  {},
  'json'
);

// Export as CSV
const csvData = await service.exportData(
  'MATCH (p:Patient) RETURN p.patientId, p.name',
  {},
  'csv'
);

// Export as Cypher
const cypherData = await service.exportData(
  'MATCH (p:Patient) RETURN p',
  {},
  'cypher'
);
```

## 🐛 Troubleshooting

### Common Issues

1. **Connection Failed**
   - Check environment variables
   - Verify network connectivity
   - Ensure correct credentials

2. **Query Timeout**
   - Optimize query performance
   - Add appropriate indexes
   - Reduce result set size

3. **Rate Limit Exceeded**
   - Reduce query frequency
   - Implement query batching
   - Increase rate limit if needed

### Debug Mode
```javascript
const service = new Neo4jService();
service.enableDebugMode(); // Enable detailed logging
```

## 🤝 Contributing

1. Follow the existing code structure
2. Add tests for new features
3. Update documentation
4. Ensure security best practices

## 📄 License

This Neo4j feature is part of the HealthHub Research Platform and follows the same MIT license.
