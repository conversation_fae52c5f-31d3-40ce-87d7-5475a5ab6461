/**
 * Optimized Neo4j proxy endpoint for glucose data and healthcare queries
 * POST /api/neo4j-optimized { query: string, parameters?: object, database?: string }
 * Returns: { records: Array<object>, summary: {...}, performance: {...} }
 * 
 * Features:
 * - Connection pooling and retry logic
 * - Query optimization for glucose data
 * - Enhanced error handling and monitoring
 * - Performance metrics and caching
 * - Specialized glucose data endpoints
 */

import neo4j from 'neo4j-driver';

// CORS headers for browser requests
const CORS_HEADERS = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  'Content-Type': 'application/json'
};

// Connection pool and performance tracking
let driverInstance = null;
let connectionMetrics = {
  totalQueries: 0,
  successfulQueries: 0,
  failedQueries: 0,
  avgResponseTime: 0,
  lastHealthCheck: null,
  connectionPool: {
    active: 0,
    idle: 0,
    total: 0
  }
};

// Query cache for frequently accessed data
const queryCache = new Map();
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes

// Get or create driver instance with optimized connection pooling
function getDriver(env) {
  if (!driverInstance) {
    const config = {
      uri: env.NEO4J_URI || env.VITE_NEO4J_URI || 'bolt://localhost:7687',
      username: env.NEO4J_USERNAME || env.VITE_NEO4J_USERNAME || 'neo4j',
      password: env.NEO4J_PASSWORD || env.VITE_NEO4J_PASSWORD,
      database: env.NEO4J_DATABASE || env.VITE_NEO4J_DATABASE || 'neo4j'
    };

    driverInstance = neo4j.driver(
      config.uri,
      neo4j.auth.basic(config.username, config.password),
      {
        maxConnectionPoolSize: 15,
        connectionAcquisitionTimeout: 20000,
        maxTransactionRetryTime: 20000,
        connectionTimeout: 15000,
        disableLosslessIntegers: true,
        logging: {
          level: 'info',
          logger: (level, message) => console.log(`[Neo4j ${level}] ${message}`)
        }
      }
    );
  }
  return driverInstance;
}

// Optimized glucose data queries with performance improvements
const GLUCOSE_QUERY_OPTIMIZATIONS = {
  'glucose_recent': {
    query: `
      MATCH (p:Patient {patientId: $patientId})-[:HAD_READING]->(g:GlucoseReading)
      WHERE g.timestamp >= datetime() - duration({days: $days})
      RETURN g.timestamp, g.glucose, g.readingType
      ORDER BY g.timestamp DESC
      LIMIT $limit
    `,
    cacheable: true,
    cacheKey: (params) => `glucose_recent_${params.patientId}_${params.days}_${params.limit}`
  },
  'glucose_stats': {
    query: `
      MATCH (p:Patient {patientId: $patientId})-[:HAD_READING]->(g:GlucoseReading)
      WHERE g.timestamp >= datetime() - duration({days: $days})
      RETURN 
        min(g.glucose) AS minGlucose,
        max(g.glucose) AS maxGlucose,
        avg(g.glucose) AS avgGlucose,
        stdev(g.glucose) AS stdDevGlucose,
        count(g) AS numberOfReadings,
        p.name AS patientName
    `,
    cacheable: true,
    cacheKey: (params) => `glucose_stats_${params.patientId}_${params.days}`
  },
  'glucose_agp': {
    query: `
      MATCH (p:Patient {patientId: $patientId})-[:HAD_READING]->(g:GlucoseReading)
      WHERE g.timestamp >= datetime() - duration({days: $days})
      WITH g, 
           g.timestamp.hour * 60 + g.timestamp.minute AS minuteOfDay
      RETURN 
        minuteOfDay,
        g.glucose,
        g.timestamp,
        g.readingType
      ORDER BY minuteOfDay, g.timestamp
    `,
    cacheable: true,
    cacheKey: (params) => `glucose_agp_${params.patientId}_${params.days}`
  },
  'glucose_time_in_range': {
    query: `
      MATCH (p:Patient {patientId: $patientId})-[:HAD_READING]->(g:GlucoseReading)
      WHERE g.timestamp >= datetime() - duration({days: $days})
      WITH collect(g.glucose) AS glucoseValues, count(g) AS totalReadings
      RETURN 
        totalReadings,
        size([g IN glucoseValues WHERE g < 70]) AS belowRange,
        size([g IN glucoseValues WHERE g >= 70 AND g <= 180]) AS inRange,
        size([g IN glucoseValues WHERE g > 180 AND g <= 250]) AS aboveRange,
        size([g IN glucoseValues WHERE g > 250]) AS veryHigh,
        round(100.0 * size([g IN glucoseValues WHERE g >= 70 AND g <= 180]) / totalReadings, 1) AS timeInRangePercent
    `,
    cacheable: true,
    cacheKey: (params) => `glucose_tir_${params.patientId}_${params.days}`
  }
};

// Update performance metrics
function _updateMetrics(startTime, success) {
  const responseTime = Date.now() - startTime;
  connectionMetrics.avgResponseTime = 
    (connectionMetrics.avgResponseTime * connectionMetrics.totalQueries + responseTime) / 
    (connectionMetrics.totalQueries + 1);
  
  if (success) {
    connectionMetrics.successfulQueries++;
  } else {
    connectionMetrics.failedQueries++;
  }
}

// Check cache for query results
function getCachedResult(cacheKey) {
  const cached = queryCache.get(cacheKey);
  if (cached && Date.now() - cached.timestamp < CACHE_TTL) {
    return cached.data;
  }
  return null;
}

// Store result in cache
function setCachedResult(cacheKey, data) {
  queryCache.set(cacheKey, {
    data,
    timestamp: Date.now()
  });
  
  // Clean old cache entries
  if (queryCache.size > 100) {
    const oldestKey = queryCache.keys().next().value;
    queryCache.delete(oldestKey);
  }
}

// Enhanced health check with connection pool metrics
async function _handleHealthCheck(env) {
  const startTime = Date.now();
  
  try {
    const driver = getDriver(env);
    const session = driver.session();
    
    try {
      const result = await session.run('RETURN datetime() as serverTime');
      const serverTime = result.records[0].get('serverTime').toString();
      
      connectionMetrics.lastHealthCheck = new Date().toISOString();
      
      return new Response(JSON.stringify({
        healthy: true,
        timestamp: new Date().toISOString(),
        serverTime,
        responseTime: Date.now() - startTime,
        metrics: connectionMetrics,
        cacheStats: {
          size: queryCache.size,
          ttl: CACHE_TTL
        },
        database: env.NEO4J_DATABASE || env.VITE_NEO4J_DATABASE || 'neo4j',
        uri: (env.NEO4J_URI || env.VITE_NEO4J_URI || '').replace(/\/\/.*@/, '//***@')
      }), {
        status: 200,
        headers: CORS_HEADERS
      });
    } finally {
      await session.close();
    }
  } catch (error) {
    console.error('Health check failed:', error);
    return new Response(JSON.stringify({
      healthy: false,
      timestamp: new Date().toISOString(),
      error: error.message,
      metrics: connectionMetrics
    }), {
      status: 503,
      headers: CORS_HEADERS
    });
  }
}

// Enhanced query execution with optimization and caching
async function _handleQuery(request, env) {
  const startTime = Date.now();
  
  try {
    const body = await request.json();
    
    if (!body || typeof body.query !== 'string') {
      return new Response(JSON.stringify({ 
        error: 'Invalid payload: expected { query, parameters?, optimized? }' 
      }), {
        status: 400,
        headers: CORS_HEADERS
      });
    }

    const cypher = body.query.trim();
    const parameters = body.parameters || {};
    const useOptimized = body.optimized || false;
    
    // Check for optimized glucose queries
    if (useOptimized && body.queryType && GLUCOSE_QUERY_OPTIMIZATIONS[body.queryType]) {
      const optimization = GLUCOSE_QUERY_OPTIMIZATIONS[body.queryType];
      
      // Check cache first
      if (optimization.cacheable) {
        const cacheKey = optimization.cacheKey(parameters);
        const cached = getCachedResult(cacheKey);
        if (cached) {
          return new Response(JSON.stringify({
            ...cached,
            cached: true,
            responseTime: Date.now() - startTime
          }), {
            status: 200,
            headers: CORS_HEADERS
          });
        }
      }
      
      // Execute optimized query
      const result = await _executeQuery(optimization.query, parameters, env);
      
      // Cache result if applicable
      if (optimization.cacheable && result.records) {
        const cacheKey = optimization.cacheKey(parameters);
        setCachedResult(cacheKey, result);
      }
      
      return new Response(JSON.stringify({
        ...result,
        optimized: true,
        responseTime: Date.now() - startTime
      }), {
        status: 200,
        headers: CORS_HEADERS
      });
    }

    // Security check for regular queries
    const forbidden = /(\bCREATE\b|\bMERGE\b|\bDELETE\b|DETACH\s+DELETE|\bSET\b|\bREMOVE\b|CALL\s+db\.|\bUSE\b|LOAD\s+CSV)/i;
    if (forbidden.test(cypher)) {
      return new Response(JSON.stringify({ 
        error: 'Write/administrative operations not allowed' 
      }), {
        status: 400,
        headers: CORS_HEADERS
      });
    }

    // Execute regular query
    const result = await _executeQuery(cypher, parameters, env);
    
    return new Response(JSON.stringify({
      ...result,
      responseTime: Date.now() - startTime
    }), {
      status: 200,
      headers: CORS_HEADERS
    });

  } catch (error) {
    console.error('Query execution error:', error);
    return new Response(JSON.stringify({
      error: 'Query execution failed',
      message: error.message,
      timestamp: new Date().toISOString()
    }), {
      status: 500,
      headers: CORS_HEADERS
    });
  }
}

// Core query execution function
async function _executeQuery(cypher, parameters, env) {
  const driver = getDriver(env);
  const session = driver.session({
    database: env.NEO4J_DATABASE || env.VITE_NEO4J_DATABASE || 'neo4j'
  });

  try {
    const result = await session.run(cypher, parameters);
    
    const records = result.records.map(record => {
      const obj = {};
      record.keys.forEach(key => {
        const value = record.get(key);
        obj[key] = neo4j.isDateTime(value) ? value.toString() : 
                   neo4j.isInt(value) ? value.toNumber() : value;
      });
      return obj;
    });

    return {
      records,
      summary: {
        queryType: result.summary.queryType,
        counters: result.summary.counters,
        resultAvailableAfter: result.summary.resultAvailableAfter,
        resultConsumedAfter: result.summary.resultConsumedAfter
      }
    };
  } finally {
    await session.close();
  }
}

export async function onRequest(context) {
  const { request, env } = context;
  const startTime = Date.now();
  
  // Handle CORS preflight
  if (request.method === 'OPTIONS') {
    return new Response(null, { status: 200, headers: CORS_HEADERS });
  }

  try {
    connectionMetrics.totalQueries++;
    
    // GET: Health check
    if (request.method === 'GET') {
      const result = await _handleHealthCheck(env);
      _updateMetrics(startTime, true);
      return result;
    }

    // POST: Execute query
    if (request.method === 'POST') {
      const result = await _handleQuery(request, env);
      _updateMetrics(startTime, true);
      return result;
    }

    return new Response(JSON.stringify({ error: 'Method not allowed' }), {
      status: 405,
      headers: CORS_HEADERS
    });

  } catch (error) {
    _updateMetrics(startTime, false);
    console.error('Handler error:', error);
    return new Response(JSON.stringify({ 
      error: 'Internal server error',
      timestamp: new Date().toISOString()
    }), {
      status: 500,
      headers: CORS_HEADERS
    });
  }
}
