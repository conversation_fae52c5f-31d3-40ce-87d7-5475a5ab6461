.dashboard {
  padding: 20px;
  background: #f8f9fa;
  min-height: 100vh;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 2px solid #e9ecef;
}

.header h2 {
  margin: 0;
  color: #2c3e50;
  font-size: 28px;
  font-weight: 600;
}

.controls {
  display: flex;
  align-items: center;
  gap: 16px;
}

.autoRefreshToggle {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #6c757d;
  cursor: pointer;
}

.autoRefreshToggle input[type="checkbox"] {
  margin: 0;
}

.refreshButton {
  padding: 8px 16px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.refreshButton:hover:not(:disabled) {
  background: #0056b3;
}

.refreshButton:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
}

.wideCard {
  grid-column: 1 / -1;
}

.card h3 {
  margin: 0 0 16px 0;
  color: #2c3e50;
  font-size: 18px;
  font-weight: 600;
}

.card h4 {
  margin: 0 0 12px 0;
  color: #495057;
  font-size: 14px;
  font-weight: 600;
}

/* Health Status */
.healthCard {
  border-left: 4px solid #28a745;
}

.status {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.status.unhealthy {
  color: #dc3545;
}

.status.healthy {
  color: #28a745;
}

.statusIcon {
  font-size: 24px;
}

.statusText {
  font-size: 18px;
  font-weight: 600;
}

.errorMessage {
  background: #f8d7da;
  color: #721c24;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 14px;
  margin-bottom: 8px;
}

.lastCheck {
  font-size: 12px;
  color: #6c757d;
}

/* Statistics */
.stats {
  display: grid;
  gap: 12px;
}

.stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f1f3f4;
}

.stat:last-child {
  border-bottom: none;
}

.statLabel {
  font-size: 14px;
  color: #6c757d;
}

.statValue {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

/* Rate Limiting */
.rateLimit {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.rateLimitBar {
  height: 8px;
  background: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
}

.rateLimitFill {
  height: 100%;
  background: linear-gradient(90deg, #28a745, #ffc107, #dc3545);
  transition: width 0.3s ease;
}

.rateLimitText {
  font-size: 14px;
  color: #6c757d;
  text-align: center;
}

/* Query Lists */
.queryList {
  max-height: 300px;
  overflow-y: auto;
}

.queryItem {
  padding: 12px;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  margin-bottom: 8px;
  background: #f8f9fa;
}

.queryItem:last-child {
  margin-bottom: 0;
}

.slowQuery {
  border-left: 4px solid #ffc107;
  background: #fff3cd;
}

.queryText {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  color: #2c3e50;
  margin-bottom: 8px;
  word-break: break-all;
}

.queryMeta {
  display: flex;
  gap: 12px;
  align-items: center;
  font-size: 12px;
}

.queryTime {
  background: #007bff;
  color: white;
  padding: 2px 6px;
  border-radius: 3px;
  font-weight: 600;
}

.queryTimestamp {
  color: #6c757d;
}

.queryError {
  color: #dc3545;
  font-weight: 600;
}

/* Schema */
.schemaGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.schemaSection {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.schemaList {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.schemaItem {
  background: #e9ecef;
  color: #495057;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.schemaMore {
  background: #6c757d;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.schemaUpdated {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e9ecef;
  font-size: 12px;
  color: #6c757d;
  text-align: center;
}

/* Loading and Error States */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  font-size: 18px;
  color: #6c757d;
}

.error {
  text-align: center;
  padding: 40px;
}

.error h3 {
  color: #dc3545;
  margin-bottom: 16px;
}

.error p {
  color: #6c757d;
  margin-bottom: 20px;
}

.retryButton {
  padding: 10px 20px;
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
}

.retryButton:hover {
  background: #c82333;
}

.noData {
  text-align: center;
  color: #6c757d;
  font-style: italic;
  padding: 20px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .dashboard {
    padding: 16px;
  }
  
  .header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .controls {
    width: 100%;
    justify-content: space-between;
  }
  
  .grid {
    grid-template-columns: 1fr;
  }
  
  .schemaGrid {
    grid-template-columns: 1fr;
  }
}
