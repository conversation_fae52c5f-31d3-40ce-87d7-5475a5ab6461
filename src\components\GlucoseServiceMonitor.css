/* Glucose Service Monitor Styles */

.glucose-service-monitor {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin: 20px 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.monitor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #e9ecef;
}

.monitor-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.4rem;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.clear-cache-btn {
  background: #ff6b6b;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s;
}

.clear-cache-btn:hover {
  background: #ff5252;
}

.monitor-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.monitor-card {
  background: white;
  border-radius: 6px;
  padding: 16px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #667eea;
}

.monitor-card h4 {
  margin: 0 0 12px 0;
  color: #2c3e50;
  font-size: 1.1rem;
}

/* Health Status */
.health-details {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.health-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  border-radius: 4px;
}

.health-indicator.healthy {
  background: #e8f5e8;
  color: #2e7d32;
}

.health-indicator.unhealthy {
  background: #ffebee;
  color: #c62828;
}

.status-icon {
  font-size: 1.2rem;
}

.status-text {
  font-weight: 600;
}

.error-message {
  background: #ffebee;
  color: #c62828;
  padding: 8px;
  border-radius: 4px;
  font-size: 0.9rem;
}

.backend-info {
  background: #f5f5f5;
  padding: 8px;
  border-radius: 4px;
  font-size: 0.9rem;
}

/* Performance Metrics */
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 12px;
}

.metric-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 4px;
}

.metric-label {
  font-size: 0.8rem;
  color: #6c757d;
  font-weight: 500;
}

.metric-value {
  font-size: 1.1rem;
  font-weight: 600;
  color: #2c3e50;
}

.metric-value.cache-rate {
  color: #28a745;
}

.metric-value.error-count {
  color: #dc3545;
}

/* Quick Actions */
.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.action-btn {
  padding: 10px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.2s;
  text-align: left;
}

.action-btn.clear-btn {
  background: #ff6b6b;
  color: white;
}

.action-btn.clear-btn:hover {
  background: #ff5252;
}

.action-btn.refresh-btn {
  background: #4CAF50;
  color: white;
}

.action-btn.refresh-btn:hover {
  background: #45a049;
}

.action-btn.update-btn {
  background: #2196F3;
  color: white;
}

.action-btn.update-btn:hover {
  background: #1976D2;
}

/* Performance Tips */
.performance-tips {
  background: white;
  border-radius: 6px;
  padding: 16px;
  border-left: 4px solid #ffc107;
}

.performance-tips h4 {
  margin: 0 0 12px 0;
  color: #2c3e50;
}

.performance-tips ul {
  margin: 0;
  padding-left: 20px;
}

.performance-tips li {
  margin-bottom: 6px;
  color: #495057;
  font-size: 0.9rem;
}

/* Compact Monitor */
.glucose-monitor-compact {
  position: relative;
  display: inline-block;
}

.monitor-toggle {
  background: #4CAF50;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8rem;
  transition: all 0.2s;
}

.monitor-toggle:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.monitor-popup {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid #ddd;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  min-width: 250px;
  margin-top: 4px;
}

.monitor-popup .monitor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border-bottom: 1px solid #eee;
  margin-bottom: 0;
}

.monitor-popup .monitor-header h4 {
  margin: 0;
  font-size: 1rem;
}

.monitor-popup .monitor-header button {
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  color: #999;
}

.monitor-content {
  padding: 12px;
}

.health-status {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.status-indicator.healthy {
  color: #4CAF50;
}

.status-indicator.unhealthy {
  color: #f44336;
}

.metrics-summary {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.metrics-summary .metric {
  display: flex;
  justify-content: space-between;
  font-size: 0.9rem;
}

.metrics-summary .metric span:first-child {
  color: #666;
}

.metrics-summary .metric span:last-child {
  font-weight: 600;
}

.loading {
  color: #666;
  font-style: italic;
  text-align: center;
  padding: 20px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .monitor-grid {
    grid-template-columns: 1fr;
  }
  
  .metrics-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .glucose-service-monitor {
    padding: 15px;
    margin: 15px 0;
  }
  
  .monitor-header {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }
}
