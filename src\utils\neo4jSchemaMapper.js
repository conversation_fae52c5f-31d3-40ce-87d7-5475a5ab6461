/**
 * Neo4j Schema Mapper Utility
 * 
 * This utility provides functions to map and validate Neo4j data structures
 * to ensure consistency between the database schema and frontend expectations.
 * 
 * Created as part of the comprehensive schema alignment fix.
 */

/**
 * Maps glucose reading properties to handle schema variations
 * @param {Object} glucoseReading - Raw glucose reading from Neo4j
 * @returns {Object} Normalized glucose reading
 */
export function mapGlucoseReading(glucoseReading) {
  if (!glucoseReading) return null;

  return {
    timestamp: glucoseReading.timestamp,
    // Handle both g.value (new schema) and g.glucose (legacy schema)
    value: glucoseReading.value ?? glucoseReading.glucose ?? null,
    glucose: glucoseReading.value ?? glucoseReading.glucose ?? null,
    // Provide default values for optional properties
    trend: glucoseReading.trend ?? 'stable',
    readingType: glucoseReading.readingType ?? 'continuous',
    // Preserve any additional properties
    ...glucoseReading
  };
}

/**
 * Maps patient properties to handle schema variations
 * @param {Object} patient - Raw patient data from Neo4j
 * @returns {Object} Normalized patient data
 */
export function mapPatientData(patient) {
  if (!patient) return null;

  // Calculate mapped values first
  const mappedValues = {
    patientId: patient.patientId,
    name: patient.name,
    age: Number(patient.age) || null,
    gender: patient.gender,
    condition: patient.condition,
    // Handle phone number variations
    phoneNumber: patient.phone ?? patient.phoneNumber ?? null,
    phone: patient.phone ?? patient.phoneNumber ?? null,
    email: patient.email,
    insurance: patient.insurance,
    // Handle diagnosis date variations
    diagnosisDate: patient.diagnosisDate ?? patient.diagnosis_date ?? null,
    diagnosis_date: patient.diagnosisDate ?? patient.diagnosis_date ?? null,
    // D1NAMO specific properties
    diabetesDuration: Number(patient.diabetesDuration) || null,
    baselineHbA1c: Number(patient.baselineHbA1c) || null,
    // Data availability metrics with proper number conversion
    dailyECGSummaryCount: Number(patient.dailyECGSummaryCount || patient.ecgReadingCount || 0),
    ecgReadingCount: Number(patient.dailyECGSummaryCount || patient.ecgReadingCount || 0),
    glucoseReadingCount: Number(patient.glucoseReadingCount || 0),
    totalOriginalECGReadings: Number(patient.totalOriginalECGReadings || 0),
    dataAvailability: patient.dataAvailability || 'unknown',
    earliestReading: patient.earliestReading,
    latestReading: patient.latestReading
  };

  // Preserve additional properties but don't override mapped values
  return {
    ...patient,
    ...mappedValues
  };
}

/**
 * Maps ECG data properties to handle schema variations
 * @param {Object} ecgData - Raw ECG data from Neo4j
 * @returns {Object} Normalized ECG data
 */
export function mapECGData(ecgData) {
  if (!ecgData) return null;

  return {
    date: ecgData.date,
    timestamp: ecgData.timestamp,
    durationHours: Number(ecgData.durationHours) || null,
    originalReadingCount: Number(ecgData.originalReadingCount || ecgData.readingCount) || 0,
    readingCount: Number(ecgData.originalReadingCount || ecgData.readingCount) || 0,
    heartRate: Number(ecgData.heartRate || ecgData.estimatedHeartRate) || null,
    estimatedHeartRate: Number(ecgData.heartRate || ecgData.estimatedHeartRate) || null,
    ecgMean: Number(ecgData.ecgMean) || null,
    ecgMin: Number(ecgData.ecgMin) || null,
    ecgMax: Number(ecgData.ecgMax) || null,
    ecgStdDev: Number(ecgData.ecgStdDev) || null,
    // Preserve any additional properties
    ...ecgData
  };
}

/**
 * Validates that required properties exist in a data object
 * @param {Object} data - Data object to validate
 * @param {Array<string>} requiredFields - Array of required field names
 * @returns {Object} Validation result with isValid boolean and missing fields array
 */
export function validateRequiredFields(data, requiredFields) {
  if (!data || typeof data !== 'object') {
    return {
      isValid: false,
      missingFields: requiredFields,
      errors: ['Data object is null or not an object']
    };
  }

  const missingFields = requiredFields.filter(field => {
    const value = data[field];
    return value === null || value === undefined || value === '';
  });

  return {
    isValid: missingFields.length === 0,
    missingFields,
    errors: missingFields.map(field => `Missing required field: ${field}`)
  };
}

/**
 * Maps synchronized ECG-Glucose data to handle schema variations
 * @param {Object} syncData - Raw synchronized data from Neo4j
 * @returns {Object} Normalized synchronized data
 */
export function mapSynchronizedData(syncData) {
  if (!syncData) return null;

  return {
    timestamp: syncData.timestamp,
    heartRate: Number(syncData.heartRate) || null,
    hrv: Number(syncData.hrv) || null,
    qtcInterval: Number(syncData.qtcInterval) || null,
    rrInterval: Number(syncData.rrInterval) || null,
    // Handle glucose value variations
    glucoseValue: Number(syncData.glucoseValue || syncData.glucose || syncData.value) || null,
    glucose: Number(syncData.glucoseValue || syncData.glucose || syncData.value) || null,
    value: Number(syncData.glucoseValue || syncData.glucose || syncData.value) || null,
    ecgId: syncData.ecgId,
    glucoseId: syncData.glucoseId,
    signalQuality: syncData.signalQuality || 'unknown',
    originalReadingCount: Number(syncData.originalReadingCount) || 0,
    ecgMean: Number(syncData.ecgMean) || null,
    ecgMin: Number(syncData.ecgMin) || null,
    ecgMax: Number(syncData.ecgMax) || null,
    durationHours: Number(syncData.durationHours) || null,
    syncTimeDiff: Number(syncData.syncTimeDiff) || null,
    // Preserve any additional properties
    ...syncData
  };
}

/**
 * Batch processes an array of data objects through a mapping function
 * @param {Array} dataArray - Array of data objects
 * @param {Function} mapperFunction - Function to map each object
 * @returns {Array} Array of mapped objects, filtering out null results
 */
export function batchMapData(dataArray, mapperFunction) {
  if (!Array.isArray(dataArray)) {
    console.warn('batchMapData: Expected array, got:', typeof dataArray);
    return [];
  }

  return dataArray
    .map(mapperFunction)
    .filter(Boolean); // Remove null/undefined results
}

/**
 * Creates a safe property accessor that handles nested properties and provides defaults
 * @param {Object} obj - Object to access properties from
 * @param {string} path - Dot-notation path to property (e.g., 'patient.details.name')
 * @param {*} defaultValue - Default value if property doesn't exist
 * @returns {*} Property value or default
 */
export function safeGet(obj, path, defaultValue = null) {
  if (!obj || typeof obj !== 'object') return defaultValue;
  
  const keys = path.split('.');
  let current = obj;
  
  for (const key of keys) {
    if (current === null || current === undefined || !(key in current)) {
      return defaultValue;
    }
    current = current[key];
  }
  
  return current;
}

/**
 * Logs schema mismatches for debugging purposes
 * @param {string} component - Component name where mismatch occurred
 * @param {string} expectedProperty - Expected property name
 * @param {string} actualProperty - Actual property name found
 * @param {Object} data - The data object for context
 */
export function logSchemaMismatch(component, expectedProperty, actualProperty, data) {
  console.warn(`Schema mismatch in ${component}:`, {
    expected: expectedProperty,
    actual: actualProperty,
    availableProperties: Object.keys(data || {}),
    data: data
  });
}

/**
 * Default export object with all mapping functions
 */
export default {
  mapGlucoseReading,
  mapPatientData,
  mapECGData,
  validateRequiredFields,
  mapSynchronizedData,
  batchMapData,
  safeGet,
  logSchemaMismatch
};
