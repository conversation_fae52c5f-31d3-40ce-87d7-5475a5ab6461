import {
    CategoryScale,
    Chart as ChartJS,
    Legend,
    LinearScale,
    LineElement,
    PointElement,
    ScatterController,
    Title,
    Tooltip,
} from 'chart.js';
import { useEffect, useMemo, useState } from 'react';
import { Line, Scatter } from 'react-chartjs-2';
import './ECGGlucoseCorrelation.css';

ChartJS.register(
    CategoryScale,
    LinearScale,
    PointElement,
    LineElement,
    Title,
    Tooltip,
    Legend,
    ScatterController
);

const ECGGlucoseCorrelation = ({
    synchronizedData = [],
    patientInfo = {},
    timeRange = 24, // hours
    onDataPointClick
}) => {
    const [activeView, setActiveView] = useState('correlation');
    const [selectedMetric, setSelectedMetric] = useState('heartRate');
    const [correlationStats, setCorrelationStats] = useState({});
    const [showTrendLine, setShowTrendLine] = useState(true);
    const [showVerboseDescription, setShowVerboseDescription] = useState(false);

    // ECG metrics available for correlation analysis
    const ECG_METRICS = [
        { key: 'heartRate', name: 'Heart Rate (BPM)', unit: 'bpm', color: '#FF6384' },
        { key: 'rrInterval', name: 'RR Interval', unit: 'ms', color: '#36A2EB' },
        { key: 'hrv', name: 'Heart Rate Variability (RMSSD)', unit: 'ms', color: '#FFCE56' },
        { key: 'qtcInterval', name: 'QTc Interval', unit: 'ms', color: '#4BC0C0' },
    ];

    // Calculate correlation coefficient
    const calculateCorrelation = (xValues, yValues) => {
        if (xValues.length !== yValues.length || xValues.length < 2) return 0;

        const n = xValues.length;
        const sumX = xValues.reduce((a, b) => a + b, 0);
        const sumY = yValues.reduce((a, b) => a + b, 0);
        const sumXY = xValues.reduce((sum, x, i) => sum + x * yValues[i], 0);
        const sumX2 = xValues.reduce((sum, x) => sum + x * x, 0);
        const sumY2 = yValues.reduce((sum, y) => sum + y * y, 0);

        const numerator = n * sumXY - sumX * sumY;
        const denominator = Math.sqrt((n * sumX2 - sumX * sumX) * (n * sumY2 - sumY * sumY));

        return denominator === 0 ? 0 : numerator / denominator;
    };

    // Process data for correlation analysis
    const processedData = useMemo(() => {
        if (!synchronizedData?.length) return { scatter: [], timeSeries: [], stats: {}, qualityMetrics: {} };

        // Filter for valid synchronized data with quality assessment
        const validData = synchronizedData.filter(d => {
            const hasValidGlucose = d && d.glucose && !isNaN(d.glucose) && typeof d.glucose === 'number';
            const hasValidECG = d && d[selectedMetric] && !isNaN(d[selectedMetric]) && typeof d[selectedMetric] === 'number';
            const isReasonableSync = !d.syncTimeDiff || d.syncTimeDiff <= 15; // Within 15 minutes
            
            return hasValidGlucose && hasValidECG && isReasonableSync;
        });

        // Sort by synchronization quality and take best synchronized points
        const sortedData = validData.sort((a, b) => {
            const aSync = a.syncTimeDiff || 0;
            const bSync = b.syncTimeDiff || 0;
            return aSync - bSync;
        });

        const bestData = sortedData.slice(0, 500); // Limit for performance

        // Scatter plot data with quality indicators
        const scatterData = bestData.map((point, index) => ({
            x: point.glucose,
            y: point[selectedMetric],
            timestamp: point.timestamp,
            syncQuality: point.syncTimeDiff || 0,
            signalQuality: point.signalQuality || 'unknown',
            index
        }));

        // Time series data
        const timeSeriesData = bestData.map((point, index) => ({
            timestamp: new Date(point.timestamp),
            glucose: point.glucose,
            ecgMetric: point[selectedMetric],
            syncQuality: point.syncTimeDiff || 0,
            index
        })).sort((a, b) => a.timestamp - b.timestamp);

        // Calculate statistics
        const glucoseValues = bestData.map(d => d.glucose);
        const ecgValues = bestData.map(d => d[selectedMetric]);
        const syncTimes = bestData.map(d => d.syncTimeDiff || 0);

        const correlation = calculateCorrelation(glucoseValues, ecgValues);

        const stats = {
            correlation: correlation,
            correlationStrength: Math.abs(correlation) > 0.7 ? 'Strong' :
                Math.abs(correlation) > 0.4 ? 'Moderate' : 'Weak',
            dataPoints: bestData.length,
            totalAvailable: synchronizedData.length,
            glucoseRange: {
                min: Math.min(...glucoseValues),
                max: Math.max(...glucoseValues),
                avg: glucoseValues.reduce((a, b) => a + b, 0) / glucoseValues.length
            },
            ecgRange: {
                min: Math.min(...ecgValues),
                max: Math.max(...ecgValues),
                avg: ecgValues.reduce((a, b) => a + b, 0) / ecgValues.length
            }
        };

        const qualityMetrics = {
            averageSyncTime: syncTimes.length > 0 ? syncTimes.reduce((a, b) => a + b, 0) / syncTimes.length : 0,
            perfectSync: syncTimes.filter(t => t === 0).length,
            within5min: syncTimes.filter(t => t <= 5).length,
            within15min: syncTimes.filter(t => t <= 15).length,
            dataUtilization: synchronizedData.length > 0 ? (bestData.length / synchronizedData.length) * 100 : 0
        };

        return { scatter: scatterData, timeSeries: timeSeriesData, stats, qualityMetrics };
    }, [synchronizedData, selectedMetric]);

    // Update correlation stats
    useEffect(() => {
        setCorrelationStats(processedData.stats);
    }, [processedData.stats]);

    // Scatter plot configuration
    const scatterChartData = {
        datasets: [
            {
                label: `${ECG_METRICS.find(m => m.key === selectedMetric)?.name || selectedMetric} vs Glucose`,
                data: processedData.scatter,
                backgroundColor: ECG_METRICS.find(m => m.key === selectedMetric)?.color || '#36A2EB',
                borderColor: ECG_METRICS.find(m => m.key === selectedMetric)?.color || '#36A2EB',
                borderWidth: 1,
                pointRadius: 4,
                pointHoverRadius: 6,
            }
        ]
    };

    const scatterOptions = {
        responsive: true,
        maintainAspectRatio: false,
        aspectRatio: 2,
        layout: { padding: 10 },
        // Constrain canvas to container width
        plugins: {
            legend: { display: true, position: 'top' },
            title: {
                display: true,
                text: `ECG-Glucose Correlation: ${ECG_METRICS.find(m => m.key === selectedMetric)?.name || selectedMetric} vs Glucose`,
                font: { size: 16, weight: 'bold' }
            },
            tooltip: {
                callbacks: {
                    title: (tooltipItems) => {
                        const point = processedData.scatter[tooltipItems[0].dataIndex];
                        return point.timestamp ? new Date(point.timestamp).toLocaleString() : '';
                    },
                    label: (context) => {
                        const point = processedData.scatter[context.dataIndex];
                        return [
                            `Glucose: ${context.parsed.x} mg/dL`,
                            `${ECG_METRICS.find(m => m.key === selectedMetric)?.name}: ${context.parsed.y} ${ECG_METRICS.find(m => m.key === selectedMetric)?.unit}`
                        ];
                    }
                }
            }
        },
        parsing: false,
        normalized: true,
        spanGaps: true,
        animation: false,
        scales: {
            x: { title: { display: true, text: 'Glucose (mg/dL)' }, min: 50, max: 300 },
            y: { title: { display: true, text: `${ECG_METRICS.find(m => m.key === selectedMetric)?.name || selectedMetric} (${ECG_METRICS.find(m => m.key === selectedMetric)?.unit || ''})` } }
        },
        onResize(chart, size) {
            chart.resize(undefined, undefined);
        },
        onClick: (event, elements) => {
            if (elements.length > 0 && onDataPointClick) {
                const pointIndex = elements[0].index;
                const dataPoint = processedData.scatter[pointIndex];
                onDataPointClick(dataPoint);
            }
        }
    };

    // Time series configuration
    const timeSeriesData = {
        labels: processedData.timeSeries.map(d => d.timestamp.toLocaleTimeString()),
        datasets: [
            {
                label: 'Glucose (mg/dL)',
                data: processedData.timeSeries.map(d => d.glucose),
                borderColor: '#28a745',
                backgroundColor: 'rgba(40, 167, 69, 0.1)',
                yAxisID: 'y',
                tension: 0.1,
            },
            {
                label: `${ECG_METRICS.find(m => m.key === selectedMetric)?.name || selectedMetric}`,
                data: processedData.timeSeries.map(d => d.ecgMetric),
                borderColor: ECG_METRICS.find(m => m.key === selectedMetric)?.color || '#36A2EB',
                backgroundColor: `${ECG_METRICS.find(m => m.key === selectedMetric)?.color || '#36A2EB'}20`,
                yAxisID: 'y1',
                tension: 0.1,
            }
        ]
    };

    const timeSeriesOptions = {
        responsive: true,
        maintainAspectRatio: false,
        aspectRatio: 2,
        layout: { padding: 10 },
        plugins: {
            legend: { display: true, position: 'top' },
            title: {
                display: true,
                text: `Time Series: Glucose and ${ECG_METRICS.find(m => m.key === selectedMetric)?.name}`,
                font: { size: 16, weight: 'bold' }
            }
        },
        normalized: true,
        spanGaps: true,
        animation: false,
        scales: {
            x: { title: { display: true, text: 'Time' } },
            y: {
                type: 'linear', display: true, position: 'left',
                title: { display: true, text: 'Glucose (mg/dL)' }, min: 50, max: 300
            },
            y1: {
                type: 'linear', display: true, position: 'right',
                title: { display: true, text: `${ECG_METRICS.find(m => m.key === selectedMetric)?.name} (${ECG_METRICS.find(m => m.key === selectedMetric)?.unit})` },
                grid: { drawOnChartArea: false }
            }
        },
        onResize(chart, size) {
            chart.resize(undefined, undefined);
        }
    };

    // Correlation interpretation
    const getCorrelationInterpretation = (correlation) => {
        const absCorr = Math.abs(correlation);
        if (absCorr < 0.1) return 'No correlation';
        if (absCorr < 0.3) return 'Weak correlation';
        if (absCorr < 0.5) return 'Moderate correlation';
        if (absCorr < 0.7) return 'Strong correlation';
        return 'Very strong correlation';
    };

    // Verbose correlation description for educational purposes
    const getVerboseCorrelationDescription = (correlation, selectedMetric) => {
        const absCorr = Math.abs(correlation);
        const isPositive = correlation > 0;
        const metricName = ECG_METRICS.find(m => m.key === selectedMetric)?.name || selectedMetric;

        const baseDescription = `
            The correlation coefficient (r = ${correlation.toFixed(3)}) measures the linear relationship between
            glucose levels and ${metricName} in this dataset. This value ranges from -1 to +1, where:
        `;

        const rangeExplanation = `
            • +1 indicates a perfect positive correlation (as one variable increases, the other increases proportionally)
            • 0 indicates no linear relationship between the variables
            • -1 indicates a perfect negative correlation (as one variable increases, the other decreases proportionally)
        `;

        let strengthInterpretation = '';
        if (absCorr < 0.1) {
            strengthInterpretation = `
                The current correlation of ${correlation.toFixed(3)} indicates virtually no linear relationship between
                glucose levels and ${metricName}. This suggests that changes in glucose do not predict changes in
                this ECG parameter in a linear fashion.
            `;
        } else if (absCorr < 0.3) {
            strengthInterpretation = `
                The current correlation of ${correlation.toFixed(3)} indicates a weak ${isPositive ? 'positive' : 'negative'}
                relationship. While there is some association between glucose levels and ${metricName},
                the relationship is not strong enough to make reliable predictions.
            `;
        } else if (absCorr < 0.5) {
            strengthInterpretation = `
                The current correlation of ${correlation.toFixed(3)} indicates a moderate ${isPositive ? 'positive' : 'negative'}
                relationship. This suggests a meaningful association where ${isPositive ? 'higher' : 'lower'} glucose levels
                tend to be associated with ${isPositive ? 'higher' : 'lower'} ${metricName} values.
            `;
        } else if (absCorr < 0.7) {
            strengthInterpretation = `
                The current correlation of ${correlation.toFixed(3)} indicates a strong ${isPositive ? 'positive' : 'negative'}
                relationship. This suggests that glucose levels and ${metricName} are closely related, with
                ${isPositive ? 'increases' : 'decreases'} in glucose generally corresponding to
                ${isPositive ? 'increases' : 'decreases'} in this ECG parameter.
            `;
        } else {
            strengthInterpretation = `
                The current correlation of ${correlation.toFixed(3)} indicates a very strong ${isPositive ? 'positive' : 'negative'}
                relationship. This suggests that glucose levels and ${metricName} are highly correlated, indicating
                a robust physiological connection between glucose metabolism and this cardiac parameter.
            `;
        }

        const clinicalContext = `
            Clinical Significance: In diabetes research, correlations between glucose levels and ECG parameters
            can reveal important insights about cardiovascular health. ${isPositive ? 'Positive' : 'Negative'} correlations
            may indicate how glucose fluctuations affect cardiac function, potentially informing treatment strategies
            and monitoring protocols.
        `;

        const limitations = `
            Important Notes: Correlation does not imply causation. This analysis shows statistical association
            but does not prove that changes in glucose directly cause changes in ECG parameters. Additional factors
            such as medication, physical activity, stress, and individual patient characteristics may influence
            both variables independently.
        `;

        return {
            baseDescription: baseDescription.trim(),
            rangeExplanation: rangeExplanation.trim(),
            strengthInterpretation: strengthInterpretation.trim(),
            clinicalContext: clinicalContext.trim(),
            limitations: limitations.trim()
        };
    };

    const getCorrelationColor = (correlation) => {
        const absCorr = Math.abs(correlation);
        if (absCorr < 0.3) return '#6c757d';
        if (absCorr < 0.5) return '#ffc107';
        if (absCorr < 0.7) return '#fd7e14';
        return '#dc3545';
    };

    return (
        <div className="ecg-glucose-correlation">
            <div className="correlation-header">
                <div className="patient-info">
                    <h3>ECG-Glucose Analysis</h3>
                    {patientInfo.name && (
                        <p className="patient-name">Patient: {patientInfo.name}</p>
                    )}
                    {patientInfo.condition && (
                        <p className="patient-condition">Condition: {patientInfo.condition}</p>
                    )}
                </div>

                <div className="view-controls">
                    <div className="view-tabs">
                        <button
                            className={`tab-button ${activeView === 'correlation' ? 'active' : ''}`}
                            onClick={() => setActiveView('correlation')}
                        >
                            Correlation Plot
                        </button>
                        <button
                            className={`tab-button ${activeView === 'timeseries' ? 'active' : ''}`}
                            onClick={() => setActiveView('timeseries')}
                        >
                            Time Series
                        </button>
                    </div>

                    <div className="metric-selector">
                        <label>ECG Metric:</label>
                        <select
                            value={selectedMetric}
                            onChange={(e) => setSelectedMetric(e.target.value)}
                        >
                            {ECG_METRICS.map(metric => (
                                <option key={metric.key} value={metric.key}>
                                    {metric.name}
                                </option>
                            ))}
                        </select>
                    </div>
                </div>
            </div>

            <div className="correlation-stats">
                <div className="stat-card correlation-card">
                    <div className="stat-header">
                        <span className="stat-label">Correlation Coefficient</span>
                        <div className="correlation-header-controls">
                            <span
                                className="correlation-badge"
                                style={{ backgroundColor: getCorrelationColor(correlationStats.correlation || 0) }}
                            >
                                {correlationStats.correlationStrength || 'N/A'}
                            </span>
                            <button
                                className="verbose-toggle-btn"
                                onClick={() => setShowVerboseDescription(!showVerboseDescription)}
                                title={showVerboseDescription ? 'Hide detailed explanation' : 'Show detailed explanation'}
                            >
                                {showVerboseDescription ? '📖 Hide Details' : '📚 Learn More'}
                            </button>
                        </div>
                    </div>
                    <div className="stat-value correlation-value">
                        {correlationStats.correlation ? correlationStats.correlation.toFixed(3) : 'N/A'}
                    </div>
                    <div className="stat-description">
                        {getCorrelationInterpretation(correlationStats.correlation || 0)}
                    </div>

                    {showVerboseDescription && correlationStats.correlation !== undefined && (
                        <div className="verbose-description">
                            <div className="verbose-section">
                                <h5>📊 Understanding Correlation Coefficients</h5>
                                <p>{getVerboseCorrelationDescription(correlationStats.correlation, selectedMetric).baseDescription}</p>
                                <div className="correlation-scale">
                                    <div className="scale-item">
                                        <span className="scale-value">-1.0</span>
                                        <span className="scale-label">Perfect Negative</span>
                                    </div>
                                    <div className="scale-item">
                                        <span className="scale-value">0.0</span>
                                        <span className="scale-label">No Correlation</span>
                                    </div>
                                    <div className="scale-item">
                                        <span className="scale-value">+1.0</span>
                                        <span className="scale-label">Perfect Positive</span>
                                    </div>
                                </div>
                            </div>

                            <div className="verbose-section">
                                <h5>🔍 Current Analysis</h5>
                                <p>{getVerboseCorrelationDescription(correlationStats.correlation, selectedMetric).strengthInterpretation}</p>
                            </div>

                            <div className="verbose-section">
                                <h5>🏥 Clinical Context</h5>
                                <p>{getVerboseCorrelationDescription(correlationStats.correlation, selectedMetric).clinicalContext}</p>
                            </div>

                            <div className="verbose-section warning">
                                <h5>⚠️ Important Limitations</h5>
                                <p>{getVerboseCorrelationDescription(correlationStats.correlation, selectedMetric).limitations}</p>
                            </div>
                        </div>
                    )}
                </div>

                <div className="stat-card">
                    <span className="stat-label">Data Points</span>
                    <span className="stat-value">{correlationStats.dataPoints || 0}</span>
                </div>

                <div className="stat-card">
                    <span className="stat-label">Glucose Range</span>
                    <span className="stat-value">
                        {correlationStats.glucoseRange ?
                            `${Math.round(correlationStats.glucoseRange.min)}-${Math.round(correlationStats.glucoseRange.max)} mg/dL`
                            : 'N/A'
                        }
                    </span>
                </div>

                <div className="stat-card">
                    <span className="stat-label">ECG Range</span>
                    <span className="stat-value">
                        {correlationStats.ecgRange ?
                            `${Math.round(correlationStats.ecgRange.min)}-${Math.round(correlationStats.ecgRange.max)} ${ECG_METRICS.find(m => m.key === selectedMetric)?.unit || ''}`
                            : 'N/A'
                        }
                    </span>
                </div>
            </div>

            <div className="chart-container">
                {processedData.scatter.length > 0 ? (
                    activeView === 'correlation' ? (
                        <Scatter data={scatterChartData} options={scatterOptions} />
                    ) : (
                        <Line data={timeSeriesData} options={timeSeriesOptions} />
                    )
                ) : (
                    <div className="chart-empty-state">
                        <div className="empty-chart-icon">📊</div>
                        <h4>No Data Available</h4>
                        <p>No synchronized ECG-glucose data found to display correlation analysis.</p>
                        <p className="empty-chart-suggestion">
                            Select a different time range or ensure data has been imported for this patient.
                        </p>
                    </div>
                )}
            </div>

            <div className="correlation-insights">
                <h4>Clinical Insights</h4>
                <div className="insights-grid">
                    {correlationStats.correlation && Math.abs(correlationStats.correlation) > 0.4 && (
                        <div className="insight-card">
                            <div className="insight-icon">📈</div>
                            <div className="insight-content">
                                <h5>Significant Correlation Detected</h5>
                                <p>
                                    {correlationStats.correlation > 0 ? 'Positive' : 'Negative'} correlation
                                    between glucose levels and {ECG_METRICS.find(m => m.key === selectedMetric)?.name.toLowerCase()}.
                                    This may indicate physiological interaction worth clinical investigation.
                                </p>
                            </div>
                        </div>
                    )}

                    {correlationStats.glucoseRange && correlationStats.glucoseRange.avg > 180 && (
                        <div className="insight-card warning">
                            <div className="insight-icon">⚠️</div>
                            <div className="insight-content">
                                <h5>Elevated Glucose Levels</h5>
                                <p>
                                    Average glucose level ({Math.round(correlationStats.glucoseRange.avg)} mg/dL)
                                    is above target range. Consider reviewing diabetes management.
                                </p>
                            </div>
                        </div>
                    )}

                    {selectedMetric === 'heartRate' && correlationStats.ecgRange &&
                        (correlationStats.ecgRange.avg > 100 || correlationStats.ecgRange.avg < 60) && (
                            <div className="insight-card warning">
                                <div className="insight-icon">💓</div>
                                <div className="insight-content">
                                    <h5>Heart Rate Anomaly</h5>
                                    <p>
                                        Average heart rate ({Math.round(correlationStats.ecgRange.avg)} bpm)
                                        {correlationStats.ecgRange.avg > 100 ? ' indicates tachycardia' : ' indicates bradycardia'}.
                                        Monitor for cardiac complications.
                                    </p>
                                </div>
                            </div>
                        )}

                    {processedData.scatter.length < 10 && (
                        <div className="insight-card info">
                            <div className="insight-icon">ℹ️</div>
                            <div className="insight-content">
                                <h5>Limited Data</h5>
                                <p>
                                    Only {processedData.scatter.length} synchronized data points available.
                                    More data points will improve correlation analysis accuracy.
                                </p>
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default ECGGlucoseCorrelation;
