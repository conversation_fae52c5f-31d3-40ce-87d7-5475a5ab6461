/**
 * Neo4j Feature Usage Examples
 * Comprehensive examples showing how to use all Neo4j features
 */

import { 
  Neo4jService, 
  SchemaManager, 
  BackupManager 
} from '../index.js';

// Example 1: Basic Service Usage
export async function basicServiceExample() {
  console.log('=== Basic Neo4j Service Example ===');
  
  const service = new Neo4jService();
  
  try {
    // Connect to database
    const connection = await service.connect();
    console.log('Connection result:', connection);
    
    // Execute a simple query
    const result = await service.executeQuery(
      'MATCH (p:Patient) RETURN p.name, p.patientId LIMIT 5'
    );
    console.log('Query results:', result.records);
    
    // Get service metrics
    const metrics = service.getMetrics();
    console.log('Service metrics:', metrics);
    
  } catch (error) {
    console.error('Basic service example failed:', error);
  }
}

// Example 2: Advanced Query with Parameters
export async function advancedQueryExample() {
  console.log('=== Advanced Query Example ===');
  
  const service = new Neo4jService();
  await service.connect();
  
  try {
    // Query with parameters and date filtering
    const result = await service.executeQuery(`
      MATCH (p:Patient {patientId: $patientId})-[:HAD_READING]->(g:GlucoseReading)
      WHERE g.timestamp >= datetime() - duration({days: $days})
      RETURN p.name as patient, 
             g.glucose as glucose, 
             g.timestamp as timestamp
      ORDER BY g.timestamp DESC
      LIMIT $limit
    `, {
      patientId: 'Patient_1',
      days: 7,
      limit: 20
    });
    
    console.log(`Found ${result.records.length} glucose readings`);
    
    // Calculate average glucose
    const avgGlucose = result.records.reduce((sum, record) => 
      sum + record.glucose, 0) / result.records.length;
    console.log(`Average glucose: ${avgGlucose.toFixed(1)} mg/dL`);
    
  } catch (error) {
    console.error('Advanced query example failed:', error);
  }
}

// Example 3: Schema Management
export async function schemaManagementExample() {
  console.log('=== Schema Management Example ===');
  
  const service = new Neo4jService();
  await service.connect();
  
  const schemaManager = new SchemaManager(service);
  
  try {
    // Define a schema
    schemaManager.defineSchema('healthcare', {
      nodes: {
        Patient: {
          properties: ['patientId', 'name', 'dateOfBirth'],
          required: ['patientId'],
          indexes: ['patientId']
        },
        Doctor: {
          properties: ['doctorId', 'name', 'specialty'],
          required: ['doctorId'],
          indexes: ['doctorId']
        }
      },
      relationships: {
        TREATS: {
          from: 'Doctor',
          to: 'Patient',
          properties: ['since']
        }
      }
    });
    
    // Refresh current schema
    const schema = await schemaManager.refreshSchema();
    console.log('Current schema:', {
      nodeLabels: schema.nodeLabels.length,
      relationshipTypes: schema.relationshipTypes.length,
      indexes: schema.indexes.length
    });
    
    // Analyze schema for improvements
    const analysis = await schemaManager.analyzeSchema();
    console.log('Schema analysis:', {
      suggestions: analysis.suggestions.length,
      warnings: analysis.warnings.length,
      errors: analysis.errors.length
    });
    
    // Create recommended indexes
    if (analysis.suggestions.length > 0) {
      const indexSuggestions = analysis.suggestions
        .filter(s => s.type === 'index_suggestion')
        .slice(0, 3); // Limit to first 3 suggestions
      
      console.log('Creating recommended indexes...');
      for (const suggestion of indexSuggestions) {
        try {
          await service.executeQuery(suggestion.query);
          console.log(`✓ Created index: ${suggestion.message}`);
        } catch (error) {
          console.log(`✗ Failed to create index: ${error.message}`);
        }
      }
    }
    
  } catch (error) {
    console.error('Schema management example failed:', error);
  }
}

// Example 4: Backup and Recovery
export async function backupRecoveryExample() {
  console.log('=== Backup and Recovery Example ===');
  
  const service = new Neo4jService();
  await service.connect();
  
  const backupManager = new BackupManager(service);
  
  try {
    // Create a partial backup (schema only for demo)
    console.log('Creating backup...');
    const backup = await backupManager.createFullBackup({
      format: 'json',
      includeSchema: true,
      includeData: false, // Skip data for demo
      onProgress: (progress) => {
        if (progress.stage && progress.percentage) {
          console.log(`${progress.stage}: ${progress.percentage}%`);
        }
      }
    });
    
    console.log('Backup created:', {
      format: backup.metadata.format,
      createdAt: backup.metadata.createdAt,
      hasSchema: !!backup.schema,
      hasData: !!backup.data
    });
    
    // Show schema backup contents
    if (backup.schema) {
      console.log('Schema backup contains:', {
        labels: backup.schema.labels.length,
        relationshipTypes: backup.schema.relationshipTypes.length,
        indexes: backup.schema.indexes.length,
        constraints: backup.schema.constraints.length
      });
    }
    
    // Generate migration script
    const migrationScript = backupManager.generateMigrationScript({
      indexes: [
        {
          name: 'patient_timestamp_idx',
          label: 'Patient',
          properties: ['createdAt'],
          type: 'BTREE'
        }
      ]
    });
    
    console.log('Generated migration script preview:');
    console.log(migrationScript.split('\n').slice(0, 10).join('\n'));
    
  } catch (error) {
    console.error('Backup and recovery example failed:', error);
  }
}

// Example 5: Data Export
export async function dataExportExample() {
  console.log('=== Data Export Example ===');
  
  const service = new Neo4jService();
  await service.connect();
  
  try {
    // Export patient data as JSON
    const jsonExport = await service.exportData(
      'MATCH (p:Patient) RETURN p.patientId, p.name, p.age LIMIT 5',
      {},
      'json'
    );
    
    console.log('JSON Export:', {
      recordCount: jsonExport.data.length,
      exportedAt: jsonExport.metadata.exportedAt
    });
    
    // Export as CSV
    const csvExport = await service.exportData(
      'MATCH (p:Patient) RETURN p.patientId as id, p.name as name LIMIT 5',
      {},
      'csv'
    );
    
    console.log('CSV Export preview:');
    console.log(csvExport.split('\n').slice(0, 3).join('\n'));
    
    // Export as Cypher statements
    const cypherExport = await service.exportData(
      'MATCH (p:Patient) RETURN p LIMIT 3',
      {},
      'cypher'
    );
    
    console.log('Cypher Export preview:');
    console.log(cypherExport.split('\n').slice(0, 3).join('\n'));
    
  } catch (error) {
    console.error('Data export example failed:', error);
  }
}

// Example 6: Health Monitoring
export async function healthMonitoringExample() {
  console.log('=== Health Monitoring Example ===');
  
  const service = new Neo4jService();
  await service.connect();
  
  try {
    // Perform health check
    const health = await service.performHealthCheck();
    console.log('Health check result:', health);
    
    // Execute some queries to generate metrics
    await service.executeQuery('MATCH (p:Patient) RETURN count(p)');
    await service.executeQuery('MATCH (g:GlucoseReading) RETURN count(g)');
    
    // Get comprehensive metrics
    const metrics = service.getMetrics();
    console.log('Service metrics:', {
      totalQueries: metrics.queryStats.totalQueries,
      avgExecutionTime: Math.round(metrics.queryStats.avgExecutionTime),
      cacheSize: metrics.cacheStats.size,
      isHealthy: metrics.healthMetrics.isHealthy
    });
    
    // Show recent queries
    if (metrics.queryStats.recentQueries.length > 0) {
      console.log('Recent queries:');
      metrics.queryStats.recentQueries.forEach((query, index) => {
        console.log(`  ${index + 1}. ${query.query} (${query.executionTime}ms)`);
      });
    }
    
  } catch (error) {
    console.error('Health monitoring example failed:', error);
  }
}

// Example 7: Error Handling and Fallbacks
export async function errorHandlingExample() {
  console.log('=== Error Handling Example ===');
  
  const service = new Neo4jService();
  
  try {
    // Attempt connection with invalid credentials
    const result = await service.connect({
      uri: 'bolt://invalid:7687',
      username: 'invalid',
      password: 'invalid',
      database: 'invalid'
    });
    
    console.log('Connection result (should fallback):', result);
    
    // Try to execute a query (should use mock data)
    const queryResult = await service.executeQuery(
      'MATCH (p:Patient) RETURN p LIMIT 5'
    );
    
    console.log('Query result (mock data):', {
      recordCount: queryResult.records.length,
      isMockData: true
    });
    
  } catch (error) {
    console.error('Error handling example failed:', error);
  }
}

// Run all examples
export async function runAllExamples() {
  console.log('🚀 Running Neo4j Feature Examples\n');
  
  const examples = [
    basicServiceExample,
    advancedQueryExample,
    schemaManagementExample,
    backupRecoveryExample,
    dataExportExample,
    healthMonitoringExample,
    errorHandlingExample
  ];
  
  for (const example of examples) {
    try {
      await example();
      console.log('✅ Example completed\n');
    } catch (error) {
      console.error('❌ Example failed:', error.message, '\n');
    }
  }
  
  console.log('🎉 All examples completed!');
}

// Export for use in other files
export default {
  basicServiceExample,
  advancedQueryExample,
  schemaManagementExample,
  backupRecoveryExample,
  dataExportExample,
  healthMonitoringExample,
  errorHandlingExample,
  runAllExamples
};
