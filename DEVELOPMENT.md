# Local Development Setup Guide

This guide will help you set up the HealthHub Research Platform for local development with all features working correctly, including OpenAI integration and Neo4j database connectivity.

## 🚀 Quick Start

For the fastest setup experience, run our automated setup script:

```bash
npm run setup
```

This will:
- ✅ Check prerequisites (Node.js, npm)
- ✅ Install all dependencies
- ✅ Create environment files from templates
- ✅ Validate configuration
- ✅ Test external connections
- ✅ Provide next steps

## 📋 Prerequisites

- **Node.js** 16 or later
- **npm** 7 or later
- **OpenAI API Key** (for AI features)
- **Neo4j Database** (AuraDB Free or local instance)

## 🔧 Manual Setup

If you prefer to set up manually or need to troubleshoot:

### 1. Install Dependencies

```bash
npm install
```

### 2. Environment Configuration

Create your environment files:

```bash
# Copy environment templates
cp .env.example .env
cp .dev.vars.example .dev.vars
```

Edit `.env` with your frontend configuration:
```env
# AI Configuration
VITE_AI_ENABLED=true
VITE_OPENAI_API_KEY=sk-your-openai-api-key-here
VITE_OPENAI_MODEL=gpt-4o-mini

# Neo4j Configuration (for browser)
VITE_NEO4J_USE_API=true
VITE_NEO4J_URI=neo4j+s://your-instance.databases.neo4j.io
VITE_NEO4J_USERNAME=neo4j
VITE_NEO4J_PASSWORD=your-password
VITE_NEO4J_DATABASE=neo4j
```

Edit `.dev.vars` with your server-side secrets:
```env
# Server-side Neo4j (for API endpoints)
NEO4J_URI=neo4j+s://your-instance.databases.neo4j.io
NEO4J_USERNAME=neo4j
NEO4J_PASSWORD=your-password
NEO4J_DATABASE=neo4j

# Server-side OpenAI (for API endpoints)
OPENAI_API_KEY=sk-your-openai-api-key-here
```

### 3. Validate Configuration

```bash
npm run validate:env
```

This will check that all required environment variables are properly configured.

### 4. Test Connections

```bash
npm run setup:env
```

This will test your OpenAI and Neo4j connections to ensure they're working.

## 🏃‍♂️ Running the Development Server

### Option 1: Full Stack (Recommended)
```bash
npm run dev:local
```
This starts both the Vite frontend and Wrangler API server.

### Option 2: Frontend Only
```bash
npm run dev
```
Starts only the Vite development server at http://localhost:5173

### Option 3: API Only
```bash
npm run dev:api
```
Starts only the Wrangler Pages Functions server at http://localhost:8787

## 🔗 Local URLs

When running the full stack:
- **Frontend**: http://localhost:5173
- **API Server**: http://localhost:8787
- **Health Check**: http://localhost:8787/api/health
- **AI Endpoint**: http://localhost:8787/api/ai
- **Neo4j Endpoint**: http://localhost:8787/api/neo4j

## 🧪 Testing Your Setup

Run the comprehensive test suite:

```bash
npm run test:local
```

This will:
- Start the API server
- Test all endpoints
- Verify AI integration
- Verify Neo4j connectivity
- Test frontend build

## 📚 Available Scripts

| Script | Description |
|--------|-------------|
| `npm run setup` | Complete automated setup |
| `npm run dev:local` | Start full development environment |
| `npm run dev` | Start frontend only |
| `npm run dev:api` | Start API server only |
| `npm run build` | Build for production |
| `npm run preview` | Preview production build |
| `npm run lint` | Check code quality |
| `npm run validate:env` | Validate environment configuration |
| `npm run setup:env` | Test external connections |
| `npm run test:local` | Test local development setup |
| `npm run health:check` | Quick API health check |

## 🔍 Troubleshooting

### Common Issues

#### 1. "OpenAI API key not configured"
- Ensure your OpenAI API key is set in both `.env` and `.dev.vars`
- Verify the key starts with `sk-`
- Check that `VITE_AI_ENABLED=true` in `.env`

#### 2. "Neo4j connection failed"
- Verify your Neo4j URI, username, and password
- For AuraDB, ensure you're using `neo4j+s://` protocol
- Check that your database is running and accessible

#### 3. "API endpoints not responding"
- Make sure Wrangler is running: `npm run dev:api`
- Check that `.dev.vars` file exists and contains server secrets
- Verify the API is accessible at http://localhost:8787

#### 4. "Environment validation failed"
- Run `npm run validate:env` to see specific issues
- Ensure all required variables are set in the correct files
- Check for typos in variable names

### Getting Help

1. **Check the logs**: Look at the console output for specific error messages
2. **Validate environment**: Run `npm run validate:env`
3. **Test connections**: Run `npm run setup:env`
4. **Full test**: Run `npm run test:local`

## 🔐 Security Notes

- Never commit `.env` or `.dev.vars` files to version control
- Keep your OpenAI API keys secure and rotate them regularly
- Use environment variables for all sensitive configuration
- For production, use Cloudflare Pages environment variables instead of `.dev.vars`

## 🚀 Production Deployment

For production deployment:

1. Set environment variables in Cloudflare Pages dashboard
2. Use `wrangler pages secret put` for sensitive values
3. Run `npm run build` to create production build
4. Deploy using `npm run deploy` or GitHub Actions

## 📖 Additional Resources

- [Cloudflare Pages Documentation](https://developers.cloudflare.com/pages/)
- [Wrangler CLI Documentation](https://developers.cloudflare.com/workers/wrangler/)
- [Neo4j AuraDB Documentation](https://neo4j.com/docs/aura/)
- [OpenAI API Documentation](https://platform.openai.com/docs/)

---

For more information, see the main [README.md](./README.md) file.
