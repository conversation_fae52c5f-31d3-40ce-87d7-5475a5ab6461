.d1namo-dashboard {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  padding: 1rem;
  height: 100%;
  background: var(--gb-bg0);
  color: var(--gb-fg);
  overflow-y: auto;
}

/* Compact mode for multi-dashboard layout */
.d1namo-dashboard.compact {
  gap: 1rem;
  padding: 1rem;
  min-height: auto;
  height: 100%;
  overflow: hidden;
}

.d1namo-dashboard.compact .dashboard-header {
  padding: 1rem;
  margin-bottom: 0;
}

.d1namo-dashboard.compact .header-content h1 {
  font-size: 1.5rem;
  margin-bottom: 0.25rem;
}

.d1namo-dashboard.compact .header-content p {
  font-size: 0.9rem;
  display: none; /* Hide subtitle in compact mode */
}

.d1namo-dashboard.compact .header-controls {
  gap: 1rem;
}

.d1namo-dashboard.compact .patient-selector,
.d1namo-dashboard.compact .time-range-selector {
  min-width: 180px;
}

.d1namo-dashboard.compact .patient-selector select,
.d1namo-dashboard.compact .time-range-selector select {
  padding: 0.5rem 2rem 0.5rem 0.75rem;
  font-size: 0.85rem;
  min-height: 36px;
  background-size: 0.875rem;
  background-position: right 0.5rem center;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 2rem;
  background: linear-gradient(135deg, var(--gb-bg1) 0%, rgba(215, 153, 33, 0.12) 100%);
  color: var(--gb-fg);
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(235, 219, 178, 0.12);
}

.header-content h1 {
  margin: 0 0 0.5rem 0;
  font-size: 2rem;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.header-content p {
  margin: 0;
  font-size: 1.1rem;
  opacity: 0.9;
  font-weight: 300;
}

.header-controls {
  display: flex;
  gap: 2rem;
  align-items: flex-end;
}

/* Workflow guide styling */
.workflow-guide {
  margin-top: 1.5rem;
  padding: 1rem;
  background: rgba(235, 219, 178, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(235, 219, 178, 0.1);
}

.workflow-steps {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 2rem;
  flex-wrap: wrap;
}

.workflow-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  border-radius: 8px;
  transition: all 0.3s ease;
  position: relative;
  min-width: 100px;
}

.workflow-step.pending {
  opacity: 0.4;
  color: rgba(235, 219, 178, 0.5);
}

.workflow-step.active {
  background: linear-gradient(145deg, rgba(250, 189, 47, 0.15), rgba(250, 189, 47, 0.08));
  color: #fabd2f;
  border: 1px solid rgba(250, 189, 47, 0.3);
  box-shadow: 0 2px 8px rgba(250, 189, 47, 0.2);
  animation: pulse 2s infinite;
}

.workflow-step.completed {
  background: linear-gradient(145deg, rgba(184, 187, 38, 0.15), rgba(184, 187, 38, 0.08));
  color: #b8bb26;
  border: 1px solid rgba(184, 187, 38, 0.3);
  box-shadow: 0 2px 8px rgba(184, 187, 38, 0.2);
}

.step-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: currentColor;
  color: var(--gb-bg0);
  font-weight: 700;
  font-size: 0.9rem;
}

.step-text {
  font-size: 0.85rem;
  font-weight: 600;
  text-align: center;
}

.step-arrow {
  font-size: 1.2rem;
  animation: bounce 1.5s infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-5px); }
  60% { transform: translateY(-3px); }
}

/* Dashboard status indicator */
.dashboard-status {
  margin-top: 1rem;
  padding: 0.75rem 1rem;
  background: rgba(235, 219, 178, 0.03);
  border-radius: 6px;
  border: 1px solid rgba(235, 219, 178, 0.08);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: inline-block;
  position: relative;
}

.status-dot.idle {
  background: rgba(235, 219, 178, 0.5);
}

.status-dot.loading {
  background: #fabd2f;
  animation: pulse 1.5s infinite;
}

.status-dot.success {
  background: #b8bb26;
  box-shadow: 0 0 8px rgba(184, 187, 38, 0.4);
}

.status-dot.warning {
  background: #fe8019;
  animation: pulse 2s infinite;
}

.status-dot.error {
  background: #fb4934;
  animation: pulse 1s infinite;
}

.status-text {
  font-size: 0.8rem;
  color: rgba(235, 219, 178, 0.8);
  font-weight: 500;
}

.keyboard-indicator {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  background: rgba(250, 189, 47, 0.1);
  border: 1px solid rgba(250, 189, 47, 0.2);
  border-radius: 4px;
  font-size: 0.75rem;
  color: #fabd2f;
  animation: fadeIn 0.3s ease-out;
}

/* Enhanced loading overlay */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(40, 40, 40, 0.9);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rem;
  padding: 3rem;
  background: linear-gradient(145deg, var(--gb-bg1), rgba(235, 219, 178, 0.05));
  border-radius: 16px;
  border: 1px solid rgba(235, 219, 178, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  max-width: 400px;
  text-align: center;
}

.loading-spinner {
  position: relative;
  width: 60px;
  height: 60px;
}

.spinner-ring {
  position: absolute;
  width: 100%;
  height: 100%;
  border: 3px solid transparent;
  border-top: 3px solid var(--gb-accent);
  border-radius: 50%;
  animation: spin 1.2s linear infinite;
}

.spinner-ring:nth-child(2) {
  width: 80%;
  height: 80%;
  top: 10%;
  left: 10%;
  border-top-color: #83a598;
  animation-duration: 1.5s;
  animation-direction: reverse;
}

.spinner-ring:nth-child(3) {
  width: 60%;
  height: 60%;
  top: 20%;
  left: 20%;
  border-top-color: #b8bb26;
  animation-duration: 1.8s;
}

.loading-content h3 {
  margin: 0 0 1rem 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--gb-fg);
}

.loading-content p {
  margin: 0 0 1.5rem 0;
  font-size: 1rem;
  color: rgba(235, 219, 178, 0.8);
  line-height: 1.4;
}

.loading-progress {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.progress-bar {
  width: 100%;
  height: 4px;
  background: rgba(235, 219, 178, 0.2);
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--gb-accent), #83a598, #b8bb26);
  border-radius: 2px;
  animation: progressFill 2s ease-in-out infinite;
}

.progress-text {
  font-size: 0.85rem;
  color: rgba(235, 219, 178, 0.7);
  font-style: italic;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes progressFill {
  0% { width: 0%; }
  50% { width: 70%; }
  100% { width: 100%; }
}

/* Keyboard navigation and accessibility */
.d1namo-dashboard.keyboard-navigation *:focus {
  outline: 3px solid var(--gb-accent) !important;
  outline-offset: 2px !important;
  box-shadow: 0 0 0 5px rgba(250, 189, 47, 0.2) !important;
}

.keyboard-shortcuts-help {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1001;
  background: linear-gradient(145deg, var(--gb-bg1), rgba(235, 219, 178, 0.05));
  border: 1px solid rgba(235, 219, 178, 0.3);
  border-radius: 12px;
  padding: 1rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  animation: slideInRight 0.3s ease-out;
  max-width: 300px;
}

.shortcuts-content h4 {
  margin: 0 0 1rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--gb-fg);
  text-align: center;
}

.shortcuts-grid {
  display: grid;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.shortcut-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
  padding: 0.25rem 0;
}

.shortcut-item kbd {
  background: rgba(235, 219, 178, 0.1);
  border: 1px solid rgba(235, 219, 178, 0.2);
  border-radius: 4px;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  font-family: monospace;
  color: var(--gb-accent);
  min-width: 60px;
  text-align: center;
}

.shortcut-item span {
  font-size: 0.8rem;
  color: rgba(235, 219, 178, 0.8);
  flex: 1;
}

.shortcuts-note {
  font-size: 0.75rem;
  color: rgba(235, 219, 178, 0.6);
  text-align: center;
  margin: 0;
  font-style: italic;
}

.shortcuts-note kbd {
  background: rgba(235, 219, 178, 0.08);
  border: 1px solid rgba(235, 219, 178, 0.15);
  border-radius: 3px;
  padding: 0.125rem 0.25rem;
  font-size: 0.7rem;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.patient-selector,
.time-range-selector {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  min-width: 200px;
}

.patient-selector label,
.time-range-selector label {
  font-size: 0.9rem;
  font-weight: 500;
  opacity: 0.9;
}

.patient-selector select,
.time-range-selector select {
  padding: 0.75rem 1rem;
  border: 1px solid rgba(235, 219, 178, 0.3);
  border-radius: 8px;
  background: var(--gb-bg1);
  color: var(--gb-fg);
  font-size: 0.9rem;
  font-family: inherit;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 44px; /* Better touch target */
  appearance: none; /* Remove default styling */
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23ebdbb2' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 1rem;
  padding-right: 2.5rem;
}

.patient-selector select:hover,
.time-range-selector select:hover {
  border-color: rgba(235, 219, 178, 0.5);
  background: rgba(235, 219, 178, 0.08);
}

.patient-selector select:focus,
.time-range-selector select:focus {
  outline: none;
  border-color: var(--gb-accent);
  box-shadow: 0 0 0 3px rgba(250, 189, 47, 0.15);
  background: rgba(235, 219, 178, 0.1);
}

.patient-selector select:disabled,
.time-range-selector select:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background: rgba(235, 219, 178, 0.02);
}

.patient-selector select option,
.time-range-selector select option {
  background: var(--gb-bg1);
  color: var(--gb-fg);
  padding: 0.5rem;
  border: none;
}

.patient-selector select option:hover,
.time-range-selector select option:hover {
  background: rgba(235, 219, 178, 0.1);
}

.patient-selector select option:checked,
.time-range-selector select option:checked {
  background: var(--gb-accent);
  color: var(--gb-bg0);
}

/* Enhanced label styling with descriptions */
.patient-selector label,
.time-range-selector label {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  font-size: 0.95rem;
  font-weight: 600;
  color: var(--gb-fg);
}

.label-description {
  font-size: 0.8rem;
  font-weight: 400;
  color: rgba(235, 219, 178, 0.7);
  font-style: italic;
}

/* Help text styling */
.help-text {
  margin-top: 0.5rem;
  font-size: 0.8rem;
  line-height: 1.3;
}

.help-message {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-weight: 500;
}

.help-message.info {
  background: rgba(131, 165, 152, 0.1);
  color: #83a598;
  border: 1px solid rgba(131, 165, 152, 0.2);
}

.help-message.success {
  background: rgba(184, 187, 38, 0.1);
  color: #b8bb26;
  border: 1px solid rgba(184, 187, 38, 0.2);
}

.help-message.warning {
  background: rgba(250, 189, 47, 0.1);
  color: #fabd2f;
  border: 1px solid rgba(250, 189, 47, 0.2);
}

.help-message.loading {
  background: rgba(235, 219, 178, 0.1);
  color: rgba(235, 219, 178, 0.8);
  border: 1px solid rgba(235, 219, 178, 0.2);
}

/* Enhanced data availability info styling */
.data-availability-info {
  margin-top: 0.75rem;
  padding: 0.75rem;
  background: rgba(235, 219, 178, 0.03);
  border-radius: 8px;
  border: 1px solid rgba(235, 219, 178, 0.1);
}

.availability-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  font-size: 0.85rem;
  font-weight: 600;
  border: 1px solid transparent;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 0.5rem;
}

.availability-badge.complete {
  background: linear-gradient(135deg, rgba(40, 167, 69, 0.15), rgba(40, 167, 69, 0.25));
  color: #28a745;
  border-color: rgba(40, 167, 69, 0.4);
  box-shadow: 0 2px 8px rgba(40, 167, 69, 0.2);
}

.availability-badge.partial {
  background: linear-gradient(135deg, rgba(255, 193, 7, 0.15), rgba(255, 193, 7, 0.25));
  color: #ffc107;
  border-color: rgba(255, 193, 7, 0.4);
  box-shadow: 0 2px 8px rgba(255, 193, 7, 0.2);
}

.availability-badge.no-data {
  background: linear-gradient(135deg, rgba(220, 53, 69, 0.15), rgba(220, 53, 69, 0.25));
  color: #dc3545;
  border-color: rgba(220, 53, 69, 0.4);
  box-shadow: 0 2px 8px rgba(220, 53, 69, 0.2);
}

.data-quality-details {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.data-detail {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  background: rgba(235, 219, 178, 0.08);
  border: 1px solid rgba(235, 219, 178, 0.15);
  border-radius: 4px;
  font-size: 0.75rem;
  color: rgba(235, 219, 178, 0.9);
}

/* Enhanced view controls and tabs styling */
.view-controls {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 1rem;
}

.view-tabs-container {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.view-tabs-label {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.view-tabs-label > span:first-child {
  font-size: 1rem;
  font-weight: 600;
  color: var(--gb-fg);
}

.view-tabs-description {
  font-size: 0.8rem;
  color: rgba(235, 219, 178, 0.7);
  font-style: italic;
}

.view-tabs {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.5rem;
  padding: 0.75rem;
  background: linear-gradient(145deg, rgba(235, 219, 178, 0.05), rgba(235, 219, 178, 0.12));
  border-radius: 12px;
  border: 1px solid rgba(235, 219, 178, 0.2);
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.1);
}

.tab-button {
  padding: 1rem;
  border: none;
  border-radius: 10px;
  background: linear-gradient(145deg, rgba(235, 219, 178, 0.08), rgba(235, 219, 178, 0.03));
  color: rgba(235, 219, 178, 0.9);
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  min-height: 80px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  text-align: center;
  border: 1px solid rgba(235, 219, 178, 0.1);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.tab-button:hover {
  background: linear-gradient(145deg, rgba(235, 219, 178, 0.15), rgba(235, 219, 178, 0.08));
  color: var(--gb-fg);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-color: rgba(235, 219, 178, 0.3);
}

.tab-button.active {
  background: linear-gradient(145deg, var(--gb-accent), rgba(196, 154, 23, 0.9));
  color: var(--gb-bg0);
  font-weight: 600;
  box-shadow: 0 4px 16px rgba(196, 154, 23, 0.4), inset 0 1px 2px rgba(255, 255, 255, 0.2);
  border-color: rgba(196, 154, 23, 0.6);
  transform: translateY(-1px);
}

.tab-button:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(250, 189, 47, 0.3), 0 2px 4px rgba(0, 0, 0, 0.1);
}

.tab-icon {
  font-size: 1.5rem;
  display: block;
}

.tab-text {
  font-weight: 600;
  font-size: 0.9rem;
  line-height: 1.2;
}

.tab-description {
  font-size: 0.75rem;
  opacity: 0.8;
  font-weight: 400;
  line-height: 1.2;
}

.tab-button.active .tab-description {
  opacity: 0.9;
}

/* Responsive adjustments for tabs */
@media (max-width: 768px) {
  .view-tabs {
    grid-template-columns: 1fr 1fr;
    gap: 0.375rem;
    padding: 0.5rem;
  }

  .tab-button {
    min-height: 70px;
    padding: 0.75rem 0.5rem;
  }

  .tab-icon {
    font-size: 1.25rem;
  }

  .tab-text {
    font-size: 0.8rem;
  }

  .tab-description {
    font-size: 0.7rem;
  }
}

@media (max-width: 480px) {
  .view-tabs {
    grid-template-columns: 1fr;
  }
}

/* Enhanced footer actions styling with skeumorphic design */
.footer-actions {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-top: 2rem;
  padding: 1.5rem;
  background: linear-gradient(145deg, rgba(235, 219, 178, 0.03), rgba(235, 219, 178, 0.08));
  border-radius: 12px;
  border: 1px solid rgba(235, 219, 178, 0.15);
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.action-group {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  align-items: stretch;
}

.primary-actions {
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(235, 219, 178, 0.1);
}

.footer-actions button {
  padding: 1rem 1.5rem;
  border: none;
  border-radius: 10px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 60px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.25rem;
  text-align: center;
  position: relative;
  border: 1px solid transparent;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  min-width: 160px;
}

.btn-icon {
  font-size: 1.25rem;
  display: block;
}

.btn-text {
  font-weight: 600;
  font-size: 0.9rem;
  line-height: 1.2;
}

.btn-description {
  font-size: 0.75rem;
  opacity: 0.8;
  font-weight: 400;
  line-height: 1.1;
}

.primary-action {
  background: linear-gradient(145deg, rgba(131, 165, 152, 0.2), rgba(131, 165, 152, 0.1));
  color: #83a598;
  border-color: rgba(131, 165, 152, 0.4);
}

.secondary-action {
  background: linear-gradient(145deg, rgba(131, 165, 152, 0.15), rgba(131, 165, 152, 0.08));
  color: #83a598;
  border-color: rgba(131, 165, 152, 0.3);
}

.refresh-btn:hover:not(:disabled) {
  background: linear-gradient(145deg, rgba(131, 165, 152, 0.3), rgba(131, 165, 152, 0.15));
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(131, 165, 152, 0.2);
  border-color: rgba(131, 165, 152, 0.5);
}

.export-btn {
  background: linear-gradient(145deg, rgba(184, 187, 38, 0.2), rgba(184, 187, 38, 0.1));
  color: #b8bb26;
  border-color: rgba(184, 187, 38, 0.4);
}

.export-btn:hover:not(:disabled) {
  background: linear-gradient(145deg, rgba(184, 187, 38, 0.3), rgba(184, 187, 38, 0.15));
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(184, 187, 38, 0.2);
  border-color: rgba(184, 187, 38, 0.5);
}

.clear-btn {
  background: linear-gradient(145deg, rgba(251, 73, 52, 0.2), rgba(251, 73, 52, 0.1));
  color: #fb4934;
  border-color: rgba(251, 73, 52, 0.4);
}

.clear-btn:hover:not(:disabled) {
  background: linear-gradient(145deg, rgba(251, 73, 52, 0.3), rgba(251, 73, 52, 0.15));
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(251, 73, 52, 0.2);
  border-color: rgba(251, 73, 52, 0.5);
}

.footer-actions button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.footer-actions button:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(250, 189, 47, 0.3), 0 2px 4px rgba(0, 0, 0, 0.1);
}

.footer-actions button:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Responsive adjustments for footer actions */
@media (max-width: 768px) {
  .action-group {
    flex-direction: column;
  }

  .footer-actions button {
    min-width: auto;
    width: 100%;
  }
}

@media (max-width: 480px) {
  .footer-actions {
    padding: 1rem;
    gap: 1rem;
  }

  .footer-actions button {
    min-height: 50px;
    padding: 0.75rem 1rem;
  }

  .btn-icon {
    font-size: 1.1rem;
  }

  .btn-text {
    font-size: 0.85rem;
  }

  .btn-description {
    font-size: 0.7rem;
  }
}

/* Error banner styling */
.error-banner {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1.5rem;
  margin-bottom: 2rem;
  background: rgba(251, 73, 52, 0.1);
  border: 1px solid rgba(251, 73, 52, 0.3);
  border-radius: 10px;
  color: #fb4934;
  animation: slideIn 0.3s ease-out;
}

.error-banner .error-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
  margin-top: 0.25rem;
}

.error-banner .error-content {
  flex: 1;
}

.error-banner .error-content h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #fb4934;
}

.error-banner .error-content p {
  margin: 0 0 1rem 0;
  font-size: 0.95rem;
  color: rgba(251, 73, 52, 0.9);
  line-height: 1.4;
}

.error-actions {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.retry-button,
.dismiss-button {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  font-size: 0.85rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.retry-button {
  background: rgba(251, 73, 52, 0.2);
  color: #fb4934;
  border: 1px solid rgba(251, 73, 52, 0.4);
}

.retry-button:hover:not(:disabled) {
  background: rgba(251, 73, 52, 0.3);
  transform: translateY(-1px);
}

.dismiss-button {
  background: rgba(235, 219, 178, 0.1);
  color: rgba(235, 219, 178, 0.8);
  border: 1px solid rgba(235, 219, 178, 0.2);
}

.dismiss-button:hover {
  background: rgba(235, 219, 178, 0.15);
  color: var(--gb-fg);
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.error-message {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: rgba(220, 53, 69, 0.08);
  border: 1px solid rgba(220, 53, 69, 0.35);
  border-radius: 8px;
  color: #f2b8b5;
}

.error-icon {
  font-size: 2rem;
  flex-shrink: 0;
}

.error-content h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1.1rem;
  font-weight: 600;
}

.error-content p {
  margin: 0 0 1rem 0;
  font-size: 0.9rem;
}

.retry-button {
  padding: 0.5rem 1rem;
  background: rgba(220, 53, 69, 0.2);
  color: #ffb4ab;
  border: 1px solid rgba(220, 53, 69, 0.35);
  border-radius: 4px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.retry-button:hover {
  background: rgba(220, 53, 69, 0.3);
}

.patient-summary {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 2rem;
  padding: 1.5rem;
  background: var(--gb-bg1);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.25);
  border-left: 4px solid var(--gb-accent2);
  border: 1px solid rgba(235, 219, 178, 0.12);
}

.patient-info h3 {
  margin: 0 0 1rem 0;
  color: var(--gb-accent2);
  font-size: 1.5rem;
  font-weight: 600;
}

.patient-details {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.patient-details span {
  padding: 0.4rem 0.8rem;
  background: rgba(235, 219, 178, 0.08);
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
  color: var(--gb-fg);
  border: 1px solid rgba(235, 219, 178, 0.15);
}

.dashboard-stats {
  display: flex;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  min-width: 100px;
}

.stat-value {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--gb-accent2);
  font-family: 'Courier New', monospace;
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.8rem;
  color: rgba(235, 219, 178, 0.75);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  line-height: 1.2;
}

.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
  color: rgba(235, 219, 178, 0.75);
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(235, 219, 178, 0.15);
  border-top: 4px solid var(--gb-accent2);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.dashboard-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.analysis-section,
.waveform-section {
  background: var(--gb-bg1);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.25);
  overflow: hidden;
  width: 100%;
  max-width: 100%;
  border: 1px solid rgba(235, 219, 178, 0.12);
}

.analysis-section h2,
.waveform-section h2 {
  margin: 0;
  padding: 1.25rem;
  background: rgba(235, 219, 178, 0.05);
  border-bottom: 1px solid rgba(235, 219, 178, 0.12);
  color: var(--gb-fg);
  font-size: 1.25rem;
  font-weight: 600;
}

.waveform-info {
  padding: 1rem 1.25rem;
  background: rgba(235, 219, 178, 0.03);
  border-bottom: 1px solid rgba(235, 219, 178, 0.12);
}

.waveform-info p {
  margin: 0.5rem 0;
  font-size: 0.9rem;
  color: rgba(235, 219, 178, 0.85);
}

.no-data-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  background: var(--gb-bg1);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.25);
  text-align: center;
  color: rgba(235, 219, 178, 0.75);
  border: 1px solid rgba(235, 219, 178, 0.12);
}

.no-data-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.no-data-message h3 {
  margin: 0 0 1rem 0;
  color: var(--gb-accent2);
  font-size: 1.5rem;
  font-weight: 600;
}

.no-data-message p {
  margin: 0.5rem 0;
  font-size: 1rem;
  line-height: 1.5;
}

.no-data-message ul {
  text-align: left;
  margin: 1rem 0;
  padding-left: 1.5rem;
}

.no-data-message li {
  margin: 0.5rem 0;
  font-size: 0.9rem;
}

.refresh-button {
  margin-top: 1.5rem;
  padding: 0.6rem 1.25rem;
  background: transparent;
  color: var(--gb-accent2);
  border: 1px solid var(--gb-accent2);
  border-radius: 6px;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.refresh-button:hover {
  background: rgba(250, 189, 47, 0.1);
}

.dashboard-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  background: var(--gb-bg1);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.25);
  margin-top: 2rem;
  border: 1px solid rgba(235, 219, 178, 0.12);
}

.footer-info {
  flex: 1;
}

.footer-info p {
  margin: 0.5rem 0;
  font-size: 0.9rem;
  color: rgba(235, 219, 178, 0.75);
  line-height: 1.4;
}

.footer-actions {
  display: flex;
  gap: 1rem;
}

.footer-actions button {
  padding: 0.5rem 1rem;
  border: 1px solid var(--gb-accent2);
  border-radius: 4px;
  background: transparent;
  color: var(--gb-accent2);
  font-size: 0.9rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.footer-actions button:hover:not(:disabled) {
  background: rgba(250, 189, 47, 0.1);
}

.footer-actions button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .dashboard-header {
    flex-direction: column;
    gap: 1.5rem;
    align-items: stretch;
  }

  .header-controls {
    justify-content: space-between;
  }

  .patient-summary {
    flex-direction: column;
    gap: 1.5rem;
  }

  .dashboard-stats {
    justify-content: center;
  }

  .dashboard-footer {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
}

@media (max-width: 768px) {
  .d1namo-dashboard {
    padding: 1rem;
    gap: 1.5rem;
  }

  .dashboard-header {
    padding: 1.5rem;
  }

  .header-content h1 {
    font-size: 1.5rem;
  }

  .header-content p {
    font-size: 1rem;
  }

  .header-controls {
    flex-direction: column;
    gap: 1rem;
  }

  .patient-selector,
  .time-range-selector {
    min-width: unset;
  }

  .dashboard-stats {
    gap: 1rem;
    justify-content: space-around;
  }

  .stat-item {
    min-width: 80px;
  }

  .stat-value {
    font-size: 1.5rem;
  }

  .dashboard-content {
    gap: 1.5rem;
  }
}

@media (max-width: 480px) {
  .d1namo-dashboard {
    padding: 0.5rem;
  }

  .dashboard-header {
    padding: 1rem;
  }

  .header-content h1 {
    font-size: 1.25rem;
  }

  .header-content p {
    font-size: 0.9rem;
  }

  .patient-summary {
    padding: 1rem;
  }

  .dashboard-stats {
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
  }

  .footer-actions {
    flex-direction: column;
    width: 100%;
  }

  .footer-actions button {
    width: 100%;
  }
}

/* Analytics placeholder styles */
.analytics-placeholder {
  padding: 2rem;
  background: var(--gb-bg1);
  border-radius: 8px;
  border: 1px solid rgba(235, 219, 178, 0.12);
  text-align: center;
}

.analytics-placeholder p {
  margin: 0.5rem 0;
  color: var(--gb-fg);
}

.analytics-placeholder ul {
  text-align: left;
  display: inline-block;
  margin: 1rem 0;
  padding-left: 1.5rem;
}

.analytics-placeholder li {
  margin: 0.5rem 0;
  color: var(--gb-fg-2);
}

.analytics-placeholder h3 {
  color: var(--gb-yellow);
  margin-bottom: 1rem;
}

/* Data availability and quality indicators */
.data-availability-info {
  margin-top: 8px;
}

.availability-badge,
.quality-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.85rem;
  font-weight: 500;
  display: inline-block;
}

.availability-badge.complete,
.quality-badge.excellent {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.availability-badge.partial,
.quality-badge.good {
  background: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

.availability-badge.no-data,
.quality-badge.limited {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f1aeb5;
}

/* Enhanced patient summary */
.data-quality-summary {
  margin-top: 16px;
  padding: 16px;
  background: var(--gb-bg0-soft);
  border-radius: 8px;
  border: 1px solid rgba(235, 219, 178, 0.12);
}

.data-quality-summary h4 {
  margin: 0 0 12px 0;
  color: var(--gb-fg);
  font-size: 1rem;
}

.quality-metrics {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;
  margin-bottom: 8px;
}

.quality-metrics .metric {
  font-size: 0.9rem;
  color: rgba(235, 219, 178, 0.75);
}

.quality-metrics .metric strong {
  color: var(--gb-fg);
  font-weight: 600;
}

.time-range-info {
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid rgba(235, 219, 178, 0.12);
}

.time-range-info small {
  display: block;
  color: rgba(235, 219, 178, 0.6);
  font-size: 0.8rem;
  margin: 2px 0;
}

/* Enhanced no-data message */
.no-data-guidance,
.data-guidance {
  margin: 16px 0;
  padding: 16px;
  background: rgba(250, 189, 47, 0.1);
  border: 1px solid rgba(250, 189, 47, 0.3);
  border-radius: 8px;
}

.no-data-guidance p:first-child {
  font-weight: 600;
  color: var(--gb-yellow);
  margin-bottom: 8px;
}

.no-data-guidance ul,
.data-guidance ul {
  margin: 12px 0;
  padding-left: 20px;
}

.no-data-guidance li,
.data-guidance li {
  margin: 4px 0;
  color: rgba(235, 219, 178, 0.85);
}

/* Dataset summary in footer */
.dataset-summary {
  margin: 12px 0;
  padding: 12px;
  background: rgba(250, 189, 47, 0.1);
  border-radius: 6px;
  font-size: 0.9rem;
}

.dataset-summary p {
  margin: 6px 0;
  color: rgba(235, 219, 178, 0.85);
}

.dataset-summary strong {
  color: var(--gb-fg);
}

/* Patient selector enhancements */
.patient-selector select {
  font-family: 'SF Mono', 'Monaco', 'Cascadia Code', monospace;
  font-size: 0.9rem;
}

.patient-selector option {
  padding: 8px;
}

/* Loading states for quality info */
.loading-quality {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(235, 219, 178, 0.2);
  border-top: 2px solid var(--gb-accent2);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-left: 8px;
}

/* Responsive adjustments for quality indicators */
@media (max-width: 768px) {
  .quality-metrics {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .data-quality-summary {
    padding: 12px;
  }
  
  .availability-badge,
  .quality-badge {
    font-size: 0.8rem;
    padding: 3px 6px;
  }
}

/* Enhanced stat value quality indicators */
.stat-value.quality-excellent {
  color: #28a745;
}

.stat-value.quality-good {
  color: #20c997;
}

.stat-value.quality-fair {
  color: #ffc107;
}

.stat-value.quality-poor {
  color: #dc3545;
}

/* Additional compact mode styles */
.d1namo-dashboard.compact .view-tabs {
  padding: 0.5rem 1rem;
}

.d1namo-dashboard.compact .view-tabs button {
  padding: 0.5rem 1rem;
  font-size: 0.8rem;
}

.d1namo-dashboard.compact .main-content {
  padding: 1rem;
  gap: 1rem;
  max-height: calc(100vh - 300px);
  overflow-y: auto;
}

.d1namo-dashboard.compact .correlation-container,
.d1namo-dashboard.compact .recommendations-container,
.d1namo-dashboard.compact .prompt-testing-container {
  gap: 1rem;
}

.d1namo-dashboard.compact .chart-container {
  height: 250px; /* Reduced height for compact mode */
}

.d1namo-dashboard.compact .waveform-container {
  height: 200px; /* Reduced height for compact mode */
}

.d1namo-dashboard.compact .stats-grid {
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 0.75rem;
}

.d1namo-dashboard.compact .stat-card {
  padding: 0.75rem;
}

.d1namo-dashboard.compact .stat-card h4 {
  font-size: 0.8rem;
}

.d1namo-dashboard.compact .stat-card .stat-value {
  font-size: 1.2rem;
}

.d1namo-dashboard.compact .recommendations-list {
  max-height: 300px;
  overflow-y: auto;
}

.d1namo-dashboard.compact .recommendation-item {
  padding: 0.75rem;
}

.d1namo-dashboard.compact .prompt-history {
  max-height: 200px;
  overflow-y: auto;
}
