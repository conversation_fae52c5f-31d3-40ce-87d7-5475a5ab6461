import { Suspense, lazy, useState } from "react"
import "./Dashboard.css"

const Neo4jWorkbench = lazy(() => import("../features/neo4j/components/Neo4jWorkbench"))
const D1NAMODashboard = lazy(() => import("./D1NAMODashboard"))
const PatientDashboard = lazy(() => import("./PatientDashboard"))

function ErrorBoundary({ children }) {
  return (
    <Suspense fallback={<div className="loading">Loading module...</div>}>
      {children}
    </Suspense>
  )
}

function Dashboard({ user, onLogout }) {
  const [activeSection, setActiveSection] = useState('d1namo')

  const renderContent = () => {
    switch (activeSection) {
      case 'd1namo':
        return <D1NAMODashboard />
      case 'patients':
        return <PatientDashboard />
      case 'neo4j':
        return <Neo4jWorkbench />
      default:
        return <D1NAMODashboard />
    }
  }
  return (
    <div className="dashboard">
      <div className="dashboard-header">
        <div className="user-info">
          <h2>Welcome, {user.name}!</h2>
          <p>{user.role} • {user.email}</p>
        </div>
        <button className="logout-button" onClick={onLogout}>
          Logout
        </button>
      </div>

      <div className="dashboard-content">
        <nav className="dashboard-nav">
          <ul>
            <li>
              <button
                className={activeSection === 'd1namo' ? 'active' : ''}
                onClick={() => setActiveSection('d1namo')}
              >
                🧬 D1NAMO
              </button>
            </li>
            <li>
              <button
                className={activeSection === 'patients' ? 'active' : ''}
                onClick={() => setActiveSection('patients')}
              >
                👥 Patients
              </button>
            </li>
            <li>
              <button
                className={activeSection === 'neo4j' ? 'active' : ''}
                onClick={() => setActiveSection('neo4j')}
              >
                🔗 Neo4j Database
              </button>
            </li>
          </ul>
        </nav>

        <main className="dashboard-main">
          <ErrorBoundary>
            {renderContent()}
          </ErrorBoundary>
        </main>
      </div>
    </div>
  )
}

export default Dashboard
