import { useEffect, useState } from 'react';
import Neo4jService from '../services/neo4jService';
import styles from './Neo4jHealthDashboard.module.css';

function Neo4jHealthDashboard() {
  const [service] = useState(() => new Neo4jService());
  const [metrics, setMetrics] = useState(null);
  const [health, setHealth] = useState(null);
  const [schema, setSchema] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [autoRefresh, setAutoRefresh] = useState(true);

  useEffect(() => {
    loadDashboardData();
    
    let interval;
    if (autoRefresh) {
      interval = setInterval(loadDashboardData, 30000); // Refresh every 30 seconds
    }
    
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [autoRefresh]);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load all dashboard data in parallel
      const [healthData, metricsData, schemaData] = await Promise.all([
        service.performHealthCheck().catch(err => ({ healthy: false, error: err.message })),
        Promise.resolve(service.getMetrics()),
        service.getSchema ? service.getSchema() : Promise.resolve(null)
      ]);

      setHealth(healthData);
      setMetrics(metricsData);
      setSchema(schemaData);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const formatDuration = (ms) => {
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
    return `${(ms / 60000).toFixed(1)}m`;
  };

  const formatNumber = (num) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  if (loading && !metrics) {
    return (
      <div className={styles.dashboard}>
        <div className={styles.loading}>Loading dashboard...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={styles.dashboard}>
        <div className={styles.error}>
          <h3>Dashboard Error</h3>
          <p>{error}</p>
          <button onClick={loadDashboardData} className={styles.retryButton}>
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.dashboard}>
      <div className={styles.header}>
        <h2>Neo4j Health Dashboard</h2>
        <div className={styles.controls}>
          <label className={styles.autoRefreshToggle}>
            <input
              type="checkbox"
              checked={autoRefresh}
              onChange={(e) => setAutoRefresh(e.target.checked)}
            />
            Auto-refresh
          </label>
          <button 
            onClick={loadDashboardData} 
            className={styles.refreshButton}
            disabled={loading}
          >
            {loading ? 'Refreshing...' : 'Refresh'}
          </button>
        </div>
      </div>

      <div className={styles.grid}>
        {/* Health Status */}
        <div className={`${styles.card} ${styles.healthCard}`}>
          <h3>Health Status</h3>
          <div className={`${styles.status} ${health?.healthy ? styles.healthy : styles.unhealthy}`}>
            <div className={styles.statusIcon}>
              {health?.healthy ? '✅' : '❌'}
            </div>
            <div className={styles.statusText}>
              {health?.healthy ? 'Healthy' : 'Unhealthy'}
            </div>
          </div>
          {health?.error && (
            <div className={styles.errorMessage}>
              {health.error}
            </div>
          )}
          {health?.lastCheck && (
            <div className={styles.lastCheck}>
              Last check: {new Date(health.lastCheck).toLocaleTimeString()}
            </div>
          )}
        </div>

        {/* Query Statistics */}
        <div className={styles.card}>
          <h3>Query Statistics</h3>
          <div className={styles.stats}>
            <div className={styles.stat}>
              <span className={styles.statLabel}>Total Queries</span>
              <span className={styles.statValue}>
                {formatNumber(metrics?.queryStats?.totalQueries || 0)}
              </span>
            </div>
            <div className={styles.stat}>
              <span className={styles.statLabel}>Avg Execution Time</span>
              <span className={styles.statValue}>
                {formatDuration(metrics?.queryStats?.avgExecutionTime || 0)}
              </span>
            </div>
            <div className={styles.stat}>
              <span className={styles.statLabel}>Cache Hits</span>
              <span className={styles.statValue}>
                {formatNumber(metrics?.queryStats?.cacheHits || 0)}
              </span>
            </div>
            <div className={styles.stat}>
              <span className={styles.statLabel}>Errors</span>
              <span className={styles.statValue}>
                {formatNumber(metrics?.queryStats?.errorCount || 0)}
              </span>
            </div>
          </div>
        </div>

        {/* Rate Limiting */}
        <div className={styles.card}>
          <h3>Rate Limiting</h3>
          <div className={styles.rateLimit}>
            <div className={styles.rateLimitBar}>
              <div 
                className={styles.rateLimitFill}
                style={{
                  width: `${(metrics?.rateLimiter?.currentRequests || 0) / 
                           (metrics?.rateLimiter?.maxRequests || 100) * 100}%`
                }}
              />
            </div>
            <div className={styles.rateLimitText}>
              {metrics?.rateLimiter?.currentRequests || 0} / {metrics?.rateLimiter?.maxRequests || 100} requests
            </div>
          </div>
        </div>

        {/* Cache Statistics */}
        <div className={styles.card}>
          <h3>Cache</h3>
          <div className={styles.stats}>
            <div className={styles.stat}>
              <span className={styles.statLabel}>Cache Size</span>
              <span className={styles.statValue}>
                {metrics?.cacheStats?.size || 0} entries
              </span>
            </div>
            <div className={styles.stat}>
              <span className={styles.statLabel}>Cache Timeout</span>
              <span className={styles.statValue}>
                {formatDuration(metrics?.cacheStats?.timeout || 0)}
              </span>
            </div>
          </div>
        </div>

        {/* Recent Queries */}
        <div className={`${styles.card} ${styles.wideCard}`}>
          <h3>Recent Queries</h3>
          <div className={styles.queryList}>
            {metrics?.queryStats?.recentQueries?.length > 0 ? (
              metrics.queryStats.recentQueries.map((query, index) => (
                <div key={index} className={styles.queryItem}>
                  <div className={styles.queryText}>{query.query}</div>
                  <div className={styles.queryMeta}>
                    <span className={styles.queryTime}>
                      {formatDuration(query.executionTime)}
                    </span>
                    <span className={styles.queryTimestamp}>
                      {new Date(query.timestamp).toLocaleTimeString()}
                    </span>
                    {query.error && (
                      <span className={styles.queryError}>❌ {query.error}</span>
                    )}
                  </div>
                </div>
              ))
            ) : (
              <div className={styles.noData}>No recent queries</div>
            )}
          </div>
        </div>

        {/* Slow Queries */}
        <div className={`${styles.card} ${styles.wideCard}`}>
          <h3>Slow Queries (&gt; 1s)</h3>
          <div className={styles.queryList}>
            {metrics?.queryStats?.slowQueries?.length > 0 ? (
              metrics.queryStats.slowQueries.map((query, index) => (
                <div key={index} className={`${styles.queryItem} ${styles.slowQuery}`}>
                  <div className={styles.queryText}>{query.query}</div>
                  <div className={styles.queryMeta}>
                    <span className={styles.queryTime}>
                      {formatDuration(query.executionTime)}
                    </span>
                    <span className={styles.queryTimestamp}>
                      {new Date(query.timestamp).toLocaleTimeString()}
                    </span>
                  </div>
                </div>
              ))
            ) : (
              <div className={styles.noData}>No slow queries detected</div>
            )}
          </div>
        </div>

        {/* Schema Information */}
        {schema && (
          <div className={`${styles.card} ${styles.wideCard}`}>
            <h3>Schema Overview</h3>
            <div className={styles.schemaGrid}>
              <div className={styles.schemaSection}>
                <h4>Node Labels ({schema.nodeLabels?.length || 0})</h4>
                <div className={styles.schemaList}>
                  {schema.nodeLabels?.slice(0, 10).map((label, index) => (
                    <span key={index} className={styles.schemaItem}>{label}</span>
                  ))}
                  {schema.nodeLabels?.length > 10 && (
                    <span className={styles.schemaMore}>
                      +{schema.nodeLabels.length - 10} more
                    </span>
                  )}
                </div>
              </div>
              <div className={styles.schemaSection}>
                <h4>Relationships ({schema.relationshipTypes?.length || 0})</h4>
                <div className={styles.schemaList}>
                  {schema.relationshipTypes?.slice(0, 10).map((type, index) => (
                    <span key={index} className={styles.schemaItem}>{type}</span>
                  ))}
                  {schema.relationshipTypes?.length > 10 && (
                    <span className={styles.schemaMore}>
                      +{schema.relationshipTypes.length - 10} more
                    </span>
                  )}
                </div>
              </div>
            </div>
            {schema.lastUpdated && (
              <div className={styles.schemaUpdated}>
                Last updated: {new Date(schema.lastUpdated).toLocaleString()}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}

export default Neo4jHealthDashboard;
