/**
 * Neo4j Backup and Recovery Manager
 * Provides utilities for backing up and restoring Neo4j data
 */

export class BackupManager {
  constructor(neo4jService) {
    this.service = neo4jService;
    this.backupFormats = ['cypher', 'json', 'csv'];
  }

  /**
   * Create a full database backup
   */
  async createFullBackup(options = {}) {
    const {
      format = 'cypher',
      includeSchema = true,
      includeData = true,
      batchSize = 1000,
      onProgress = null
    } = options;

    const backup = {
      metadata: {
        createdAt: new Date().toISOString(),
        format,
        includeSchema,
        includeData,
        version: '1.0'
      },
      schema: null,
      data: null
    };

    try {
      // Backup schema if requested
      if (includeSchema) {
        backup.schema = await this.backupSchema();
        if (onProgress) onProgress({ stage: 'schema', completed: true });
      }

      // Backup data if requested
      if (includeData) {
        backup.data = await this.backupData(format, batchSize, onProgress);
      }

      return backup;

    } catch (error) {
      throw new Error(`Backup failed: ${error.message}`);
    }
  }

  /**
   * Backup database schema (indexes, constraints, labels, relationships)
   */
  async backupSchema() {
    const schema = {
      labels: [],
      relationshipTypes: [],
      indexes: [],
      constraints: [],
      procedures: []
    };

    try {
      // Get node labels
      const labelsResult = await this.service.executeQuery('CALL db.labels()');
      schema.labels = labelsResult.records.map(r => 
        r.label || r[Object.keys(r)[0]]
      );

      // Get relationship types
      const relsResult = await this.service.executeQuery('CALL db.relationshipTypes()');
      schema.relationshipTypes = relsResult.records.map(r => 
        r.relationshipType || r[Object.keys(r)[0]]
      );

      // Get indexes
      const indexesResult = await this.service.executeQuery('CALL db.indexes()');
      schema.indexes = indexesResult.records.map(r => ({
        name: r.name,
        labels: r.labelsOrTypes,
        properties: r.properties,
        type: r.type,
        state: r.state
      }));

      // Get constraints
      const constraintsResult = await this.service.executeQuery('CALL db.constraints()');
      schema.constraints = constraintsResult.records.map(r => ({
        name: r.name,
        description: r.description,
        labels: r.labelsOrTypes,
        properties: r.properties,
        type: r.type
      }));

      // Get procedures (if available)
      try {
        const procsResult = await this.service.executeQuery('CALL dbms.procedures()');
        schema.procedures = procsResult.records.map(r => ({
          name: r.name,
          signature: r.signature,
          description: r.description
        }));
      } catch (error) {
        console.warn('Could not backup procedures:', error.message);
      }

    } catch (error) {
      throw new Error(`Schema backup failed: ${error.message}`);
    }

    return schema;
  }

  /**
   * Backup all data in the specified format
   */
  async backupData(format, batchSize, onProgress) {
    const data = {
      nodes: {},
      relationships: []
    };

    try {
      // Get all node labels
      const labelsResult = await this.service.executeQuery('CALL db.labels()');
      const labels = labelsResult.records.map(r => 
        r.label || r[Object.keys(r)[0]]
      );

      let totalNodes = 0;
      let processedNodes = 0;

      // Count total nodes for progress tracking
      for (const label of labels) {
        const countResult = await this.service.executeQuery(
          `MATCH (n:\`${label}\`) RETURN count(n) as count`
        );
        totalNodes += countResult.records[0].count;
      }

      // Backup nodes by label
      for (const label of labels) {
        data.nodes[label] = [];
        let skip = 0;
        let hasMore = true;

        while (hasMore) {
          const nodesResult = await this.service.executeQuery(
            `MATCH (n:\`${label}\`) RETURN n SKIP $skip LIMIT $limit`,
            { skip, limit: batchSize }
          );

          if (nodesResult.records.length === 0) {
            hasMore = false;
          } else {
            const nodes = nodesResult.records.map(r => this.formatNode(r.n, format));
            data.nodes[label].push(...nodes);
            
            processedNodes += nodesResult.records.length;
            skip += batchSize;

            if (onProgress) {
              onProgress({
                stage: 'nodes',
                label,
                processed: processedNodes,
                total: totalNodes,
                percentage: Math.round((processedNodes / totalNodes) * 100)
              });
            }
          }
        }
      }

      // Backup relationships
      if (onProgress) onProgress({ stage: 'relationships', started: true });
      
      const relsResult = await this.service.executeQuery(`
        MATCH (a)-[r]->(b)
        RETURN id(a) as startId, labels(a) as startLabels,
               type(r) as relType, properties(r) as relProps,
               id(b) as endId, labels(b) as endLabels
        LIMIT 10000
      `);

      data.relationships = relsResult.records.map(r => ({
        startId: r.startId,
        startLabels: r.startLabels,
        type: r.relType,
        properties: r.relProps,
        endId: r.endId,
        endLabels: r.endLabels
      }));

      if (onProgress) onProgress({ stage: 'relationships', completed: true });

    } catch (error) {
      throw new Error(`Data backup failed: ${error.message}`);
    }

    return data;
  }

  /**
   * Format node data based on backup format
   */
  formatNode(node, format) {
    switch (format) {
      case 'cypher':
        return this.nodeToCypher(node);
      case 'json':
        return this.nodeToJSON(node);
      case 'csv':
        return this.nodeToCSV(node);
      default:
        return node;
    }
  }

  /**
   * Convert node to Cypher CREATE statement
   */
  nodeToCypher(node) {
    const labels = node.labels ? node.labels.map(l => `:\`${l}\``).join('') : '';
    const props = node.properties ? 
      Object.entries(node.properties)
        .map(([k, v]) => `\`${k}\`: ${JSON.stringify(v)}`)
        .join(', ') : '';
    
    return `CREATE (n${labels} {${props}})`;
  }

  /**
   * Convert node to JSON format
   */
  nodeToJSON(node) {
    return {
      id: node.identity?.toNumber ? node.identity.toNumber() : node.identity,
      labels: node.labels || [],
      properties: node.properties || {}
    };
  }

  /**
   * Convert node to CSV format
   */
  nodeToCSV(node) {
    return {
      id: node.identity?.toNumber ? node.identity.toNumber() : node.identity,
      labels: (node.labels || []).join(';'),
      properties: JSON.stringify(node.properties || {})
    };
  }

  /**
   * Restore database from backup
   */
  async restoreFromBackup(backup, options = {}) {
    const {
      clearExisting = false,
      restoreSchema = true,
      restoreData = true,
      batchSize = 1000,
      onProgress = null
    } = options;

    try {
      // Clear existing data if requested
      if (clearExisting) {
        await this.clearDatabase();
        if (onProgress) onProgress({ stage: 'clear', completed: true });
      }

      // Restore schema
      if (restoreSchema && backup.schema) {
        await this.restoreSchema(backup.schema);
        if (onProgress) onProgress({ stage: 'schema', completed: true });
      }

      // Restore data
      if (restoreData && backup.data) {
        await this.restoreData(backup.data, batchSize, onProgress);
      }

      return { success: true, message: 'Restore completed successfully' };

    } catch (error) {
      throw new Error(`Restore failed: ${error.message}`);
    }
  }

  /**
   * Restore schema from backup
   */
  async restoreSchema(schema) {
    // Restore indexes
    for (const index of schema.indexes || []) {
      try {
        if (index.type === 'BTREE') {
          await this.service.executeQuery(
            `CREATE INDEX \`${index.name}\` IF NOT EXISTS FOR (n:\`${index.labels[0]}\`) ON (${index.properties.map(p => `n.\`${p}\``).join(', ')})`
          );
        }
      } catch (error) {
        console.warn(`Failed to restore index ${index.name}:`, error.message);
      }
    }

    // Restore constraints
    for (const constraint of schema.constraints || []) {
      try {
        if (constraint.type === 'UNIQUENESS') {
          await this.service.executeQuery(
            `CREATE CONSTRAINT \`${constraint.name}\` IF NOT EXISTS FOR (n:\`${constraint.labels[0]}\`) REQUIRE (${constraint.properties.map(p => `n.\`${p}\``).join(', ')}) IS UNIQUE`
          );
        }
      } catch (error) {
        console.warn(`Failed to restore constraint ${constraint.name}:`, error.message);
      }
    }
  }

  /**
   * Restore data from backup
   */
  async restoreData(data, batchSize, onProgress) {
    // Restore nodes
    for (const [label, nodes] of Object.entries(data.nodes || {})) {
      let processed = 0;
      
      for (let i = 0; i < nodes.length; i += batchSize) {
        const batch = nodes.slice(i, i + batchSize);
        
        // Create nodes in batch
        const createQueries = batch.map(node => {
          if (typeof node === 'string') {
            return node; // Already a Cypher statement
          } else {
            return this.nodeToCypher(node);
          }
        });

        for (const query of createQueries) {
          try {
            await this.service.executeQuery(query);
            processed++;
          } catch (error) {
            console.warn(`Failed to restore node:`, error.message);
          }
        }

        if (onProgress) {
          onProgress({
            stage: 'nodes',
            label,
            processed,
            total: nodes.length,
            percentage: Math.round((processed / nodes.length) * 100)
          });
        }
      }
    }

    // Restore relationships
    const relationships = data.relationships || [];
    let processedRels = 0;

    for (const rel of relationships) {
      try {
        const query = `
          MATCH (a), (b)
          WHERE id(a) = $startId AND id(b) = $endId
          CREATE (a)-[r:\`${rel.type}\`]->(b)
          SET r = $props
        `;
        
        await this.service.executeQuery(query, {
          startId: rel.startId,
          endId: rel.endId,
          props: rel.properties || {}
        });
        
        processedRels++;
      } catch (error) {
        console.warn(`Failed to restore relationship:`, error.message);
      }

      if (onProgress && processedRels % 100 === 0) {
        onProgress({
          stage: 'relationships',
          processed: processedRels,
          total: relationships.length,
          percentage: Math.round((processedRels / relationships.length) * 100)
        });
      }
    }
  }

  /**
   * Clear all data from database
   */
  async clearDatabase() {
    await this.service.executeQuery('MATCH (n) DETACH DELETE n');
  }

  /**
   * Export backup to downloadable format
   */
  exportBackup(backup, filename) {
    const dataStr = JSON.stringify(backup, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename || `neo4j-backup-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }

  /**
   * Import backup from file
   */
  async importBackup(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      
      reader.onload = (e) => {
        try {
          const backup = JSON.parse(e.target.result);
          resolve(backup);
        } catch (error) {
          reject(new Error('Invalid backup file format'));
        }
      };
      
      reader.onerror = () => reject(new Error('Failed to read backup file'));
      reader.readAsText(file);
    });
  }
}

export default BackupManager;
