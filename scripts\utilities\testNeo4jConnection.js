import dotenv from 'dotenv';
import neo4j from 'neo4j-driver';

dotenv.config();

async function testNeo4jConnection() {
  let driver = null;

  try {
    console.log('🔍 Testing Neo4j connection...');

    const uri = process.env.VITE_NEO4J_URI;
    const username = process.env.VITE_NEO4J_USERNAME;
    const password = process.env.VITE_NEO4J_PASSWORD;

    console.log('Connection details:');
    console.log('- URI:', uri ? uri.substring(0, 20) + '...' : 'NOT SET');
    console.log('- Username:', username || 'NOT SET');
    console.log('- Password:', password ? '***' : 'NOT SET');

    if (!uri || !username || !password) {
      throw new Error('Neo4j connection parameters not found in environment variables');
    }

    // Create driver and test connection
    driver = neo4j.driver(uri, neo4j.auth.basic(username, password));
    const session = driver.session();

    try {
      const result = await session.run('RETURN 1 as test, datetime() as timestamp');
      const record = result.records[0];

      console.log('✅ Connection successful!');
      console.log('- Test value:', record.get('test').toNumber());
      console.log('- Server time:', record.get('timestamp').toString());

    } finally {
      await session.close();
    }

    // Test D1NAMO patients query
    console.log('🔍 Testing D1NAMO patients query...');
    const session2 = driver.session();

    try {
      const patientsResult = await session2.run(`
        MATCH (p:Patient:D1NAMOSubject)
        OPTIONAL MATCH (p)-[:HAD_ECG]->(e:ECGReading)
        RETURN p.patientId as patientId,
               p.name as name,
               p.age as age,
               p.gender as gender,
               p.condition as condition,
               p.diabetesDuration as diabetesDuration,
               p.baselineHbA1c as baselineHbA1c,
               count(e) as ecgReadingCount
        ORDER BY p.patientId
      `);

      console.log(`📊 Found ${patientsResult.records.length} D1NAMO patients`);
      patientsResult.records.forEach((record, i) => {
        const patientId = record.get('patientId');
        const name = record.get('name');
        const ecgCount = record.get('ecgReadingCount').toNumber();
        console.log(`   ${i + 1}. ${name} (${patientId}) - ${ecgCount} ECG readings`);
      });

    } finally {
      await session2.close();
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  } finally {
    if (driver) {
      await driver.close();
    }
  }
}

testNeo4jConnection();
