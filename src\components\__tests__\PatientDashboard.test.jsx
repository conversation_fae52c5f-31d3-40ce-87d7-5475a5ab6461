import React from 'react';
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import PatientDashboard from '../PatientDashboard';
import neo4jService from '../../services/neo4jService';
import AIRecommendationService from '../../services/aiService';

// Mock the services
vi.mock('../../services/neo4jService');
vi.mock('../../services/aiService');
vi.mock('../../utils/agpCalculation');
vi.mock('../../utils/agpReporting');

// Mock child components
vi.mock('../AGPChart', () => ({
    default: ({ data }) => React.createElement('div', { 'data-testid': 'agp-chart' }, `AGP Chart: ${data ? 'with data' : 'no data'}`)
}));

vi.mock('../AGPStatistics', () => ({
    default: ({ data }) => React.createElement('div', { 'data-testid': 'agp-statistics' }, `AGP Stats: ${data ? 'with data' : 'no data'}`)
}));

vi.mock('../AIControlPanel', () => ({
    default: ({ recommendations }) => React.createElement('div', { 'data-testid': 'ai-control-panel' }, `AI Panel: ${recommendations ? 'with recommendations' : 'no recommendations'}`)
}));

vi.mock('../ErrorBoundary', () => ({
    default: ({ children }) => React.createElement('div', { 'data-testid': 'error-boundary' }, children)
}));

const mockPatients = [
    {
        patientId: 'P001',
        name: 'John Doe',
        condition: 'Type 1 Diabetes',
        age: 35,
        gender: 'Male',
        insurance: 'Blue Cross',
        totalReadings: 150,
        lastReading: '2024-01-15T10:00:00Z',
        firstReading: '2024-01-01T10:00:00Z'
    },
    {
        patientId: 'P002',
        name: 'Jane Smith',
        condition: 'Type 2 Diabetes',
        age: 42,
        gender: 'Female',
        insurance: 'Aetna',
        totalReadings: 89,
        lastReading: '2024-01-14T15:30:00Z',
        firstReading: '2024-01-02T08:00:00Z'
    }
];

const mockGlucoseData = [
    { timestamp: '2024-01-15T10:00:00Z', glucose: 120 },
    { timestamp: '2024-01-15T14:00:00Z', glucose: 140 },
    { timestamp: '2024-01-15T18:00:00Z', glucose: 110 }
];

const mockPatientDetails = {
    patientId: 'P001',
    name: 'John Doe',
    primaryCondition: 'Type 1 Diabetes',
    age: 35,
    gender: 'Male',
    avgGlucose: 125,
    minGlucose: 80,
    maxGlucose: 200
};

describe('PatientDashboard', () => {
    beforeEach(() => {
        // Reset all mocks
        vi.clearAllMocks();
        
        // Setup default mock implementations
        neo4jService.connect = vi.fn().mockResolvedValue(true);
        neo4jService.runQuery = vi.fn()
            .mockResolvedValueOnce(mockPatients) // First call for patients
            .mockResolvedValueOnce(mockGlucoseData) // Second call for glucose data
            .mockResolvedValueOnce([mockPatientDetails]); // Third call for patient details
        
        AIRecommendationService.prototype.generateAGPRecommendations = vi.fn()
            .mockResolvedValue({ recommendations: ['Test recommendation'] });
    });

    afterEach(() => {
        vi.restoreAllMocks();
    });

    it('renders the dashboard with patient list', async () => {
        render(<PatientDashboard />);
        
        expect(screen.getByText('👥 Patients')).toBeInTheDocument();
        
        await waitFor(() => {
            expect(screen.getByText('John Doe')).toBeInTheDocument();
            expect(screen.getByText('Jane Smith')).toBeInTheDocument();
        });
    });

    it('loads patients on mount', async () => {
        render(<PatientDashboard />);
        
        await waitFor(() => {
            expect(neo4jService.connect).toHaveBeenCalled();
            expect(neo4jService.runQuery).toHaveBeenCalled();
        });
    });

    it('filters patients by search term', async () => {
        const user = userEvent.setup();
        render(<PatientDashboard />);
        
        await waitFor(() => {
            expect(screen.getByText('John Doe')).toBeInTheDocument();
        });
        
        const searchInput = screen.getByPlaceholderText('🔍 Search patients...');
        await user.type(searchInput, 'John');
        
        expect(screen.getByText('John Doe')).toBeInTheDocument();
        expect(screen.queryByText('Jane Smith')).not.toBeInTheDocument();
    });

    it('filters patients by condition', async () => {
        const user = userEvent.setup();
        render(<PatientDashboard />);
        
        await waitFor(() => {
            expect(screen.getByText('John Doe')).toBeInTheDocument();
        });
        
        const conditionFilter = screen.getByDisplayValue('All Conditions');
        await user.selectOptions(conditionFilter, 'diabetes');
        
        expect(screen.getByText('John Doe')).toBeInTheDocument();
        expect(screen.getByText('Jane Smith')).toBeInTheDocument();
    });

    it('sorts patients correctly', async () => {
        const user = userEvent.setup();
        render(<PatientDashboard />);
        
        await waitFor(() => {
            expect(screen.getByText('John Doe')).toBeInTheDocument();
        });
        
        const sortSelect = screen.getByDisplayValue('Latest Reading');
        await user.selectOptions(sortSelect, 'name');
        
        // Verify sorting is applied (implementation would need to check order)
        expect(screen.getByText('John Doe')).toBeInTheDocument();
        expect(screen.getByText('Jane Smith')).toBeInTheDocument();
    });

    it('selects a patient and loads their data', async () => {
        const user = userEvent.setup();
        render(<PatientDashboard />);
        
        await waitFor(() => {
            expect(screen.getByText('John Doe')).toBeInTheDocument();
        });
        
        const patientCard = screen.getByText('John Doe').closest('.patient-card');
        await user.click(patientCard);
        
        await waitFor(() => {
            expect(neo4jService.runQuery).toHaveBeenCalledTimes(3); // patients + glucose + details
        });
    });

    it('switches between different views', async () => {
        const user = userEvent.setup();
        render(<PatientDashboard />);
        
        await waitFor(() => {
            expect(screen.getByText('John Doe')).toBeInTheDocument();
        });
        
        // Select a patient first
        const patientCard = screen.getByText('John Doe').closest('.patient-card');
        await user.click(patientCard);
        
        await waitFor(() => {
            expect(screen.getByText('📊 Overview')).toBeInTheDocument();
        });
        
        // Switch to analytics view
        const analyticsTab = screen.getByText('📈 Analytics');
        await user.click(analyticsTab);
        
        await waitFor(() => {
            expect(screen.getByTestId('agp-chart')).toBeInTheDocument();
        });
    });

    it('handles comparison mode', async () => {
        const user = userEvent.setup();
        render(<PatientDashboard />);
        
        await waitFor(() => {
            expect(screen.getByText('John Doe')).toBeInTheDocument();
        });
        
        const compareButton = screen.getByText('📊 Compare Patients');
        await user.click(compareButton);
        
        expect(screen.getByText('❌ Cancel Compare')).toBeInTheDocument();
        expect(screen.getByText('0/4 selected')).toBeInTheDocument();
    });

    it('handles errors gracefully', async () => {
        neo4jService.runQuery = vi.fn().mockRejectedValue(new Error('Database connection failed'));
        
        render(<PatientDashboard />);
        
        await waitFor(() => {
            expect(screen.getByText(/Failed to load patients/)).toBeInTheDocument();
        });
    });

    it('shows loading state', () => {
        render(<PatientDashboard />);
        
        // Should show loading initially
        expect(screen.getByText('👥 Patients')).toBeInTheDocument();
    });

    it('generates reports correctly', async () => {
        const user = userEvent.setup();
        render(<PatientDashboard />);
        
        await waitFor(() => {
            expect(screen.getByText('John Doe')).toBeInTheDocument();
        });
        
        // Select patient and go to reports
        const patientCard = screen.getByText('John Doe').closest('.patient-card');
        await user.click(patientCard);
        
        await waitFor(() => {
            const reportsTab = screen.getByText('📄 Reports');
            user.click(reportsTab);
        });
        
        await waitFor(() => {
            expect(screen.getByText('📊 AGP Report')).toBeInTheDocument();
        });
    });
});
