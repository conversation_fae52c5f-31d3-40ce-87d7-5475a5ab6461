/* Neo4j Workbench Layout (Gruvbox-aligned) */
.neo4j-workbench {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.workbench-panel.single {
  border: 1px solid rgba(235, 219, 178, 0.2);
  border-radius: 10px;
  overflow: hidden;
  background: rgba(235, 219, 178, 0.06);
  min-height: 480px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.loading {
  padding: 1rem;
  color: #d79921;
}

/* Compact mode for multi-dashboard layout */
.neo4j-workbench.compact {
  height: 100%;
}

.neo4j-workbench.compact .workbench-panel.single {
  min-height: auto;
  height: 100%;
}

.neo4j-workbench.compact .loading {
  padding: 0.5rem;
  font-size: 0.9rem;
}
