// Data Export/Import Utilities for Patient Dashboard

/**
 * Export patient data to various formats
 */
export class DataExporter {
    static exportToJSON(data, filename = 'patient-data') {
        const jsonString = JSON.stringify(data, null, 2);
        this.downloadFile(jsonString, `${filename}.json`, 'application/json');
    }

    static exportToCSV(data, filename = 'patient-data') {
        if (!Array.isArray(data) || data.length === 0) {
            throw new Error('Data must be a non-empty array');
        }

        const headers = Object.keys(data[0]);
        const csvContent = [
            headers.join(','),
            ...data.map(row => 
                headers.map(header => {
                    const value = row[header];
                    // Handle values that might contain commas or quotes
                    if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
                        return `"${value.replace(/"/g, '""')}"`;
                    }
                    return value || '';
                }).join(',')
            )
        ].join('\n');

        this.downloadFile(csvContent, `${filename}.csv`, 'text/csv');
    }

    static exportPatientReport(patientData, agpData, recommendations) {
        const report = {
            exportDate: new Date().toISOString(),
            patient: {
                id: patientData.patientId,
                name: patientData.name,
                age: patientData.age,
                gender: patientData.gender,
                condition: patientData.condition
            },
            glucoseData: {
                totalReadings: patientData.glucose?.length || 0,
                dateRange: patientData.glucose?.length > 0 ? {
                    start: Math.min(...patientData.glucose.map(g => new Date(g.timestamp))),
                    end: Math.max(...patientData.glucose.map(g => new Date(g.timestamp)))
                } : null,
                averageGlucose: patientData.avgGlucose,
                minGlucose: patientData.minGlucose,
                maxGlucose: patientData.maxGlucose
            },
            agpAnalysis: agpData ? {
                timeInRange: agpData.timeInRange,
                gmi: agpData.gmi,
                summary: agpData.summary,
                calculatedAt: agpData.calculatedAt
            } : null,
            aiRecommendations: recommendations ? {
                recommendations: recommendations.recommendations || [],
                generatedAt: recommendations.generated_at,
                technique: recommendations.prompting_technique
            } : null
        };

        this.exportToJSON(report, `patient-report-${patientData.name}-${Date.now()}`);
    }

    static downloadFile(content, filename, mimeType) {
        const blob = new Blob([content], { type: mimeType });
        const url = URL.createObjectURL(blob);
        
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        link.style.display = 'none';
        
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        // Clean up the URL object
        setTimeout(() => URL.revokeObjectURL(url), 100);
    }
}

/**
 * Import patient data from files
 */
export class DataImporter {
    static async importFromFile(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            
            reader.onload = (event) => {
                try {
                    const content = event.target.result;
                    let data;
                    
                    if (file.type === 'application/json' || file.name.endsWith('.json')) {
                        data = JSON.parse(content);
                    } else if (file.type === 'text/csv' || file.name.endsWith('.csv')) {
                        data = this.parseCSV(content);
                    } else {
                        throw new Error('Unsupported file format. Please use JSON or CSV files.');
                    }
                    
                    resolve(data);
                } catch (error) {
                    reject(new Error(`Failed to parse file: ${error.message}`));
                }
            };
            
            reader.onerror = () => {
                reject(new Error('Failed to read file'));
            };
            
            reader.readAsText(file);
        });
    }

    static parseCSV(csvContent) {
        const lines = csvContent.trim().split('\n');
        if (lines.length < 2) {
            throw new Error('CSV file must have at least a header row and one data row');
        }

        const headers = lines[0].split(',').map(header => header.trim().replace(/^"|"$/g, ''));
        const data = [];

        for (let i = 1; i < lines.length; i++) {
            const values = this.parseCSVLine(lines[i]);
            if (values.length !== headers.length) {
                console.warn(`Row ${i + 1} has ${values.length} values but expected ${headers.length}`);
                continue;
            }

            const row = {};
            headers.forEach((header, index) => {
                row[header] = values[index];
            });
            data.push(row);
        }

        return data;
    }

    static parseCSVLine(line) {
        const values = [];
        let current = '';
        let inQuotes = false;
        
        for (let i = 0; i < line.length; i++) {
            const char = line[i];
            
            if (char === '"') {
                if (inQuotes && line[i + 1] === '"') {
                    // Escaped quote
                    current += '"';
                    i++; // Skip next quote
                } else {
                    // Toggle quote state
                    inQuotes = !inQuotes;
                }
            } else if (char === ',' && !inQuotes) {
                // End of field
                values.push(current.trim());
                current = '';
            } else {
                current += char;
            }
        }
        
        // Add the last field
        values.push(current.trim());
        
        return values;
    }

    static validatePatientData(data) {
        const requiredFields = ['patientId', 'name'];
        const errors = [];

        if (!Array.isArray(data)) {
            data = [data];
        }

        data.forEach((patient, index) => {
            requiredFields.forEach(field => {
                if (!patient[field]) {
                    errors.push(`Row ${index + 1}: Missing required field '${field}'`);
                }
            });

            // Validate data types
            if (patient.age && isNaN(parseInt(patient.age))) {
                errors.push(`Row ${index + 1}: Age must be a number`);
            }

            if (patient.patientId && typeof patient.patientId !== 'string') {
                errors.push(`Row ${index + 1}: Patient ID must be a string`);
            }
        });

        return {
            isValid: errors.length === 0,
            errors: errors,
            data: data
        };
    }
}

/**
 * Backup and restore functionality
 */
export class DataBackup {
    static createBackup(patients, selectedPatient, patientData) {
        const backup = {
            version: '1.0',
            createdAt: new Date().toISOString(),
            patients: patients,
            selectedPatient: selectedPatient,
            patientData: patientData,
            metadata: {
                totalPatients: patients.length,
                hasSelectedPatient: !!selectedPatient,
                hasPatientData: !!patientData && Object.keys(patientData).length > 0
            }
        };

        DataExporter.exportToJSON(backup, `dashboard-backup-${Date.now()}`);
        return backup;
    }

    static async restoreFromBackup(file) {
        try {
            const backup = await DataImporter.importFromFile(file);
            
            if (!backup.version || !backup.createdAt) {
                throw new Error('Invalid backup file format');
            }

            return {
                success: true,
                data: backup,
                message: `Backup from ${new Date(backup.createdAt).toLocaleString()} restored successfully`
            };
        } catch (error) {
            return {
                success: false,
                error: error.message,
                message: 'Failed to restore backup'
            };
        }
    }
}
