/**
 * Enhanced Glucose Data Service
 * Optimized service for glucose data retrieval with caching and performance monitoring
 */

class GlucoseDataService {
  constructor() {
    this.cache = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
    this.metrics = {
      totalRequests: 0,
      cacheHits: 0,
      avgResponseTime: 0,
      errors: 0
    };
    this.baseUrl = this.getBaseUrl();
  }

  getBaseUrl() {
    if (typeof window !== 'undefined') {
      return window.location.origin;
    }
    return 'https://healthhub-research-platform.pages.dev';
  }

  // Get cache key for a request
  getCacheKey(queryType, parameters) {
    return `${queryType}_${JSON.stringify(parameters)}`;
  }

  // Check if cached data is still valid
  isCacheValid(cacheEntry) {
    return Date.now() - cacheEntry.timestamp < this.cacheTimeout;
  }

  // Get data from cache if available and valid
  getCachedData(cacheKey) {
    const cached = this.cache.get(cacheKey);
    if (cached && this.isCacheValid(cached)) {
      this.metrics.cacheHits++;
      return cached.data;
    }
    return null;
  }

  // Store data in cache
  setCachedData(cacheKey, data) {
    this.cache.set(cacheKey, {
      data,
      timestamp: Date.now()
    });

    // Clean old cache entries if cache gets too large
    if (this.cache.size > 50) {
      const oldestKey = this.cache.keys().next().value;
      this.cache.delete(oldestKey);
    }
  }

  // Execute optimized query via API
  async executeOptimizedQuery(queryType, parameters = {}) {
    const startTime = Date.now();
    this.metrics.totalRequests++;

    try {
      // Check cache first
      const cacheKey = this.getCacheKey(queryType, parameters);
      const cached = this.getCachedData(cacheKey);
      if (cached) {
        console.log(`🎯 Cache hit for ${queryType}`);
        return cached;
      }

      console.log(`🔄 Executing optimized glucose query: ${queryType}`);

      const response = await fetch(`${this.baseUrl}/api/neo4j-optimized`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          queryType,
          parameters,
          optimized: true
        })
      });

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      
      // Update metrics
      const responseTime = Date.now() - startTime;
      this.metrics.avgResponseTime = 
        (this.metrics.avgResponseTime * (this.metrics.totalRequests - 1) + responseTime) / 
        this.metrics.totalRequests;

      // Cache the result
      this.setCachedData(cacheKey, result);

      console.log(`✅ Query ${queryType} completed in ${responseTime}ms`);
      return result;

    } catch (error) {
      this.metrics.errors++;
      console.error(`❌ Glucose query ${queryType} failed:`, error);
      throw error;
    }
  }

  // Get recent glucose readings for a patient
  async getRecentGlucoseReadings(patientId, days = 30, limit = 1000) {
    return this.executeOptimizedQuery('glucose_recent', {
      patientId,
      days,
      limit
    });
  }

  // Get glucose statistics for a patient
  async getGlucoseStatistics(patientId, days = 30) {
    return this.executeOptimizedQuery('glucose_stats', {
      patientId,
      days
    });
  }

  // Get AGP (Ambulatory Glucose Profile) data
  async getAGPData(patientId, days = 14) {
    return this.executeOptimizedQuery('glucose_agp', {
      patientId,
      days
    });
  }

  // Get Time in Range data
  async getTimeInRange(patientId, days = 30) {
    return this.executeOptimizedQuery('glucose_time_in_range', {
      patientId,
      days
    });
  }

  // Get comprehensive glucose dashboard data
  async getGlucoseDashboardData(patientId, days = 30) {
    try {
      console.log(`📊 Loading comprehensive glucose data for patient ${patientId}`);

      // Execute multiple optimized queries in parallel
      const [recentReadings, statistics, agpData, timeInRange] = await Promise.all([
        this.getRecentGlucoseReadings(patientId, days, 1000),
        this.getGlucoseStatistics(patientId, days),
        this.getAGPData(patientId, Math.min(days, 14)), // AGP typically uses 14 days
        this.getTimeInRange(patientId, days)
      ]);

      return {
        recentReadings: recentReadings.records || [],
        statistics: statistics.records?.[0] || {},
        agpData: agpData.records || [],
        timeInRange: timeInRange.records?.[0] || {},
        metadata: {
          patientId,
          days,
          timestamp: new Date().toISOString(),
          cached: recentReadings.cached || false
        }
      };

    } catch (error) {
      console.error('❌ Failed to load glucose dashboard data:', error);
      throw error;
    }
  }

  // Health check for the glucose data service
  async healthCheck() {
    try {
      const response = await fetch(`${this.baseUrl}/api/neo4j-optimized`, {
        method: 'GET'
      });

      if (!response.ok) {
        throw new Error(`Health check failed: ${response.status}`);
      }

      const health = await response.json();
      return {
        healthy: health.healthy,
        service: 'GlucoseDataService',
        metrics: this.metrics,
        cacheSize: this.cache.size,
        backend: health
      };

    } catch (error) {
      return {
        healthy: false,
        service: 'GlucoseDataService',
        error: error.message,
        metrics: this.metrics
      };
    }
  }

  // Clear cache
  clearCache() {
    this.cache.clear();
    console.log('🧹 Glucose data cache cleared');
  }

  // Get service metrics
  getMetrics() {
    return {
      ...this.metrics,
      cacheSize: this.cache.size,
      cacheHitRate: this.metrics.totalRequests > 0 ? 
        (this.metrics.cacheHits / this.metrics.totalRequests * 100).toFixed(1) + '%' : '0%'
    };
  }

  // Fallback to regular Neo4j API if optimized endpoint is not available
  async executeRegularQuery(cypher, parameters = {}) {
    const startTime = Date.now();
    
    try {
      console.log('🔄 Executing regular Neo4j query as fallback');

      const response = await fetch(`${this.baseUrl}/api/neo4j`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'query',
          query: cypher,
          parameters
        })
      });

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      
      console.log(`✅ Fallback query completed in ${Date.now() - startTime}ms`);
      return result;

    } catch (error) {
      console.error('❌ Fallback query failed:', error);
      throw error;
    }
  }

  // Get glucose readings with fallback support
  async getGlucoseReadingsWithFallback(patientId, days = 30, limit = 1000) {
    try {
      // Try optimized endpoint first
      return await this.getRecentGlucoseReadings(patientId, days, limit);
    } catch (error) {
      console.warn('⚠️ Optimized endpoint failed, using fallback');
      
      // Fallback to regular API
      const cypher = `
        MATCH (p:Patient {patientId: $patientId})-[:HAD_READING]->(g:GlucoseReading)
        WHERE g.timestamp >= datetime() - duration({days: $days})
        RETURN g.timestamp, g.glucose, g.readingType
        ORDER BY g.timestamp DESC
        LIMIT $limit
      `;
      
      return await this.executeRegularQuery(cypher, { patientId, days, limit });
    }
  }
}

// Create and export singleton instance
const glucoseDataService = new GlucoseDataService();
export default glucoseDataService;
