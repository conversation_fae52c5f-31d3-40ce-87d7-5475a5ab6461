#!/usr/bin/env node

/**
 * Local Development Environment Setup Script
 * 
 * This script helps set up the HealthHub Research Platform for local development
 * by validating environment variables, testing connections, and providing setup guidance.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.join(__dirname, '..');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSection(title) {
  log(`\n${'='.repeat(60)}`, 'cyan');
  log(`${title}`, 'bright');
  log(`${'='.repeat(60)}`, 'cyan');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

async function checkFileExists(filePath) {
  try {
    await fs.promises.access(filePath);
    return true;
  } catch {
    return false;
  }
}

async function readEnvFile(filePath) {
  try {
    const content = await fs.promises.readFile(filePath, 'utf8');
    const env = {};
    content.split('\n').forEach(line => {
      const trimmed = line.trim();
      if (trimmed && !trimmed.startsWith('#')) {
        const [key, ...valueParts] = trimmed.split('=');
        if (key && valueParts.length > 0) {
          env[key.trim()] = valueParts.join('=').trim();
        }
      }
    });
    return env;
  } catch (error) {
    return null;
  }
}

async function validateEnvironmentFiles() {
  logSection('🔧 Environment Files Validation');

  const envPath = path.join(rootDir, '.env');
  const devVarsPath = path.join(rootDir, '.dev.vars');
  const envExamplePath = path.join(rootDir, '.env.example');
  const devVarsExamplePath = path.join(rootDir, '.dev.vars.example');

  // Check .env file
  if (await checkFileExists(envPath)) {
    logSuccess('.env file exists');
    const envVars = await readEnvFile(envPath);
    
    // Validate critical environment variables
    const requiredViteVars = [
      'VITE_NEO4J_USE_API',
      'VITE_NEO4J_URI',
      'VITE_NEO4J_USERNAME',
      'VITE_OPENAI_API_KEY',
      'VITE_AI_ENABLED'
    ];

    let missingVars = [];
    requiredViteVars.forEach(varName => {
      if (!envVars[varName] || envVars[varName] === '') {
        missingVars.push(varName);
      }
    });

    if (missingVars.length === 0) {
      logSuccess('All required VITE environment variables are configured');
    } else {
      logWarning(`Missing or empty VITE variables: ${missingVars.join(', ')}`);
    }

    // Check OpenAI API key format
    if (envVars.VITE_OPENAI_API_KEY && envVars.VITE_OPENAI_API_KEY.startsWith('sk-')) {
      logSuccess('OpenAI API key format looks correct');
    } else {
      logWarning('OpenAI API key format may be incorrect (should start with "sk-")');
    }

  } else {
    logError('.env file not found');
    if (await checkFileExists(envExamplePath)) {
      logInfo('Copy .env.example to .env and configure your values');
    }
  }

  // Check .dev.vars file
  if (await checkFileExists(devVarsPath)) {
    logSuccess('.dev.vars file exists');
    const devVars = await readEnvFile(devVarsPath);
    
    const requiredDevVars = ['NEO4J_URI', 'NEO4J_USERNAME', 'NEO4J_PASSWORD', 'OPENAI_API_KEY'];
    let missingDevVars = [];
    
    requiredDevVars.forEach(varName => {
      if (!devVars[varName] || devVars[varName] === '') {
        missingDevVars.push(varName);
      }
    });

    if (missingDevVars.length === 0) {
      logSuccess('All required server-side environment variables are configured');
    } else {
      logWarning(`Missing server-side variables: ${missingDevVars.join(', ')}`);
    }
  } else {
    logError('.dev.vars file not found');
    logInfo('Create .dev.vars file for server-side secrets (used by Wrangler)');
  }
}

async function testOpenAIConnection() {
  logSection('🤖 OpenAI API Connection Test');

  try {
    const envVars = await readEnvFile(path.join(rootDir, '.env'));
    const devVars = await readEnvFile(path.join(rootDir, '.dev.vars'));
    
    const apiKey = envVars?.VITE_OPENAI_API_KEY || devVars?.OPENAI_API_KEY;
    
    if (!apiKey) {
      logError('No OpenAI API key found in environment files');
      return false;
    }

    if (!apiKey.startsWith('sk-')) {
      logWarning('OpenAI API key format may be incorrect');
      return false;
    }

    logInfo('Testing OpenAI API connection...');
    
    const response = await fetch('https://api.openai.com/v1/models', {
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.ok) {
      const data = await response.json();
      logSuccess(`OpenAI API connection successful! Available models: ${data.data.length}`);
      
      // Check for specific models
      const availableModels = data.data.map(model => model.id);
      const recommendedModels = ['gpt-4o-mini', 'gpt-3.5-turbo', 'gpt-4'];
      const hasRecommended = recommendedModels.some(model => availableModels.includes(model));
      
      if (hasRecommended) {
        logSuccess('Recommended models are available');
      } else {
        logWarning('Recommended models may not be available');
      }
      
      return true;
    } else {
      logError(`OpenAI API connection failed: ${response.status} ${response.statusText}`);
      return false;
    }
  } catch (error) {
    logError(`OpenAI API test failed: ${error.message}`);
    return false;
  }
}

async function testNeo4jConnection() {
  logSection('🗄️  Neo4j Database Connection Test');

  try {
    const envVars = await readEnvFile(path.join(rootDir, '.env'));
    const devVars = await readEnvFile(path.join(rootDir, '.dev.vars'));
    
    const uri = envVars?.VITE_NEO4J_URI || devVars?.NEO4J_URI;
    const username = envVars?.VITE_NEO4J_USERNAME || devVars?.NEO4J_USERNAME;
    const password = envVars?.VITE_NEO4J_PASSWORD || devVars?.NEO4J_PASSWORD;
    
    if (!uri || !username || !password) {
      logError('Neo4j connection details not found in environment files');
      return false;
    }

    logInfo('Testing Neo4j database connection...');
    
    // Import neo4j driver dynamically
    const neo4j = await import('neo4j-driver');
    const driver = neo4j.default.driver(uri, neo4j.default.auth.basic(username, password));
    
    const session = driver.session();
    
    try {
      const result = await session.run('RETURN "Hello, Neo4j!" as message');
      const record = result.records[0];
      const message = record.get('message');
      
      if (message === 'Hello, Neo4j!') {
        logSuccess('Neo4j database connection successful!');
        
        // Test for sample data
        const dataCheck = await session.run('MATCH (n) RETURN count(n) as nodeCount');
        const nodeCount = dataCheck.records[0].get('nodeCount').toNumber();
        
        if (nodeCount > 0) {
          logSuccess(`Database contains ${nodeCount} nodes`);
        } else {
          logWarning('Database appears to be empty - you may need to populate it');
        }
        
        return true;
      }
    } finally {
      await session.close();
      await driver.close();
    }
  } catch (error) {
    logError(`Neo4j connection test failed: ${error.message}`);
    return false;
  }
}

async function validatePackageJson() {
  logSection('📦 Package Dependencies Validation');

  try {
    const packageJsonPath = path.join(rootDir, 'package.json');
    const packageJson = JSON.parse(await fs.promises.readFile(packageJsonPath, 'utf8'));
    
    logSuccess('package.json is valid');
    
    // Check for required scripts
    const requiredScripts = ['dev', 'dev:local', 'build', 'preview'];
    const missingScripts = requiredScripts.filter(script => !packageJson.scripts[script]);
    
    if (missingScripts.length === 0) {
      logSuccess('All required npm scripts are present');
    } else {
      logWarning(`Missing npm scripts: ${missingScripts.join(', ')}`);
    }
    
    // Check for critical dependencies
    const criticalDeps = ['react', 'vite', 'neo4j-driver'];
    const missingDeps = criticalDeps.filter(dep => 
      !packageJson.dependencies[dep] && !packageJson.devDependencies[dep]
    );
    
    if (missingDeps.length === 0) {
      logSuccess('All critical dependencies are listed');
    } else {
      logWarning(`Missing critical dependencies: ${missingDeps.join(', ')}`);
    }
    
    return true;
  } catch (error) {
    logError(`Package.json validation failed: ${error.message}`);
    return false;
  }
}

async function generateSetupReport() {
  logSection('📋 Setup Report & Next Steps');

  const envExists = await checkFileExists(path.join(rootDir, '.env'));
  const devVarsExists = await checkFileExists(path.join(rootDir, '.dev.vars'));
  
  log('\n📊 Environment Status:', 'bright');
  log(`   .env file: ${envExists ? '✅ Present' : '❌ Missing'}`, envExists ? 'green' : 'red');
  log(`   .dev.vars file: ${devVarsExists ? '✅ Present' : '❌ Missing'}`, devVarsExists ? 'green' : 'red');
  
  log('\n🚀 To start local development:', 'bright');
  log('   1. npm run dev:local    # Start both Vite and Wrangler', 'cyan');
  log('   2. npm run dev          # Start Vite only', 'cyan');
  log('   3. npm run dev:api      # Start Wrangler only', 'cyan');
  
  log('\n🔗 Local URLs:', 'bright');
  log('   Frontend: http://localhost:5173', 'cyan');
  log('   API: http://127.0.0.1:8787', 'cyan');
  log('   Health Check: http://127.0.0.1:8787/api/health', 'cyan');
  
  log('\n📚 Useful Commands:', 'bright');
  log('   npm run lint           # Check code quality', 'cyan');
  log('   npm run build          # Build for production', 'cyan');
  log('   npm run preview        # Preview production build', 'cyan');
  log('   npx wrangler pages dev # Test Cloudflare Pages locally', 'cyan');
}

async function main() {
  log('🏥 HealthHub Research Platform - Local Development Setup', 'bright');
  log('Setting up your local development environment...\n', 'blue');

  try {
    await validatePackageJson();
    await validateEnvironmentFiles();
    await testOpenAIConnection();
    await testNeo4jConnection();
    await generateSetupReport();
    
    log('\n🎉 Setup validation complete!', 'green');
    log('Your local development environment is ready to use.', 'green');
    
  } catch (error) {
    logError(`Setup failed: ${error.message}`);
    process.exit(1);
  }
}

// Run the setup if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}

export { main as setupLocalDev };
