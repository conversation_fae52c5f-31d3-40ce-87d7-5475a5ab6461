/* Ultra-compact layout optimized for information density */
.query-runner {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  height: 100%;
  overflow: hidden;
  font-size: 0.95em;
}

.query-runner-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem 0;
  border-bottom: 1px solid rgba(235, 219, 178, 0.1);
}

.query-runner-header h2 {
  margin: 0;
  font-size: 1.1em;
  font-weight: 600;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 0.3rem;
  font-size: 0.85em;
  padding: 0.2rem 0.5rem;
  border-radius: 12px;
  background: rgba(235, 219, 178, 0.1);
}

.status-indicator {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #6b7280;
}

.connection-status.connected .status-indicator {
  background: #10b981;
}

.connection-status.error .status-indicator {
  background: #ef4444;
}

.query-tabs {
  display: flex;
  gap: 0.25rem;
  flex-wrap: wrap;
  background: rgba(235, 219, 178, 0.05);
  padding: 0.25rem;
  border-radius: 6px;
}

.tab-button {
  padding: 0.4rem 0.8rem;
  border: none;
  background: transparent;
  color: rgba(235, 219, 178, 0.7);
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9em;
  transition: all 0.15s ease;
}

.tab-button:hover {
  background: rgba(235, 219, 178, 0.1);
  color: rgba(235, 219, 178, 0.9);
}

.tab-button.active {
  background: rgba(235, 219, 178, 0.15);
  color: #ebdbb2;
  font-weight: 500;
}

.query-content {
  display: grid;
  grid-template-columns: 1.3fr 0.7fr;
  gap: 0.75rem;
  flex: 1;
  min-height: 0;
}

.predefined-queries,
.agp-settings-panel,
.ai-control-panel {
  overflow-y: auto;
  max-height: 100%;
  padding-right: 0.25rem;
}

.predefined-queries h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1em;
  font-weight: 600;
}

.query-categories {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.query-category {
  background: rgba(235, 219, 178, 0.03);
  border-radius: 6px;
  padding: 0.5rem;
}

.category-header {
  margin: 0 0 0.5rem 0;
  font-size: 0.9em;
  font-weight: 600;
  color: #d79921;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.query-grid {
  display: flex;
  flex-direction: column;
  gap: 0.4rem;
}

.query-editor {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  overflow-y: auto;
  background: rgba(235, 219, 178, 0.03);
  border-radius: 6px;
  padding: 0.75rem;
}

.query-editor h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1em;
  font-weight: 600;
}

.parameters-section {
  background: rgba(235, 219, 178, 0.05);
  padding: 0.5rem;
  border-radius: 4px;
  margin-bottom: 0.5rem;
}

.parameters-section h4 {
  margin: 0 0 0.4rem 0;
  font-size: 0.9em;
  font-weight: 600;
  color: #d79921;
}

.parameter-inputs {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.5rem;
}

.parameter-input {
  display: flex;
  flex-direction: column;
  gap: 0.2rem;
}

.parameter-input label {
  font-size: 0.8em;
  font-weight: 500;
  color: rgba(235, 219, 178, 0.8);
}

.parameter-input select,
.parameter-input input {
  padding: 0.5rem 2rem 0.5rem 0.75rem;
  border: 1px solid rgba(235, 219, 178, 0.3);
  border-radius: 6px;
  background: rgba(50, 48, 47, 0.9);
  color: #ebdbb2;
  font-size: 0.85em;
  font-family: inherit;
  transition: all 0.2s ease;
  min-height: 36px;
}

.parameter-input select {
  cursor: pointer;
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23ebdbb2' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 0.5rem center;
  background-size: 0.875rem;
}

.parameter-input input {
  padding: 0.5rem 0.75rem;
}

.parameter-input select:hover,
.parameter-input input:hover {
  border-color: rgba(235, 219, 178, 0.5);
  background-color: rgba(235, 219, 178, 0.05);
}

.parameter-input select:focus,
.parameter-input input:focus {
  outline: none;
  border-color: #d79921;
  box-shadow: 0 0 0 2px rgba(215, 153, 33, 0.15);
}

.query-input-section {
  display: flex;
  flex-direction: column;
  gap: 0.3rem;
  flex: 1;
}

.query-input-section label {
  font-size: 0.9em;
  font-weight: 600;
  margin-bottom: 0.2rem;
}

.query-textarea {
  flex: 1;
  min-height: 100px;
  max-height: 150px;
  resize: vertical;
  padding: 0.5rem;
  border: 1px solid rgba(235, 219, 178, 0.2);
  border-radius: 4px;
  background: rgba(40, 40, 40, 0.9);
  color: #ebdbb2;
  font-family: 'JetBrains Mono', 'Fira Code', monospace;
  font-size: 0.85em;
  line-height: 1.4;
}

.query-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.execute-button {
  background: rgba(16, 185, 129, 0.8);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.15s ease;
}

.execute-button:hover:not(:disabled) {
  background: rgba(16, 185, 129, 0.9);
}

.execute-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.export-buttons {
  display: flex;
  gap: 0.3rem;
}

.export-button {
  background: rgba(235, 219, 178, 0.1);
  color: #ebdbb2;
  border: 1px solid rgba(235, 219, 178, 0.2);
  padding: 0.4rem 0.6rem;
  border-radius: 3px;
  font-size: 0.8em;
  cursor: pointer;
  transition: all 0.15s ease;
}

.export-button:hover {
  background: rgba(235, 219, 178, 0.2);
}

.query-card-actions.compact-actions {
  display: flex;
  gap: 0;
}

.query-card-actions.compact-actions .execute-button,
.query-card-actions.compact-actions .view-button {
  border-radius: 6px 0 0 6px;
  margin: 0;
  padding: 0.4rem 0.8rem;
  font-size: 0.95em;
  min-width: 0;
  line-height: 1.2;
}

.query-card-actions.compact-actions .view-button {
  border-radius: 0 6px 6px 0;
  border-left: 1px solid rgba(235, 219, 178, 0.15);
}

.query-card {
  padding: 0.6rem 0.7rem 0.5rem 0.7rem;
  margin-bottom: 0.5rem;
  min-width: 0;
  font-size: 0.98em;
  background: rgba(235, 219, 178, 0.07);
  border-radius: 8px;
  border: 1px solid rgba(235, 219, 178, 0.13);
  box-shadow: none;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  gap: 0.7rem;
}

.query-card-content {
  flex: 1 1 auto;
  min-width: 0;
  margin-right: 0.5rem;
}

.query-card h5 {
  margin: 0 0 0.1em 0;
  font-size: 1em;
  font-weight: 600;
}

.query-card p {
  margin: 0 0 0.2em 0;
  font-size: 0.97em;
  opacity: 0.92;
}

.parameters-info {
  margin: 0.1em 0 0 0;
  font-size: 0.92em;
  opacity: 0.7;
}

.query-textarea {
  resize: vertical;
  min-height: 120px;
  max-height: 200px;
}

.query-actions {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.results-section {
  border-top: 1px solid rgba(235, 219, 178, 0.2);
  padding-top: 0.5rem;
  margin-top: 0.5rem;
}

.query-stats {
  background: rgba(235, 219, 178, 0.05);
  padding: 0.5rem;
  border-radius: 4px;
  margin-bottom: 0.5rem;
}

.query-stats h3 {
  margin: 0 0 0.5rem 0;
  font-size: 0.9em;
  font-weight: 600;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 0.5rem;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  padding: 0.3rem 0.4rem;
  background: rgba(235, 219, 178, 0.08);
  border-radius: 3px;
  font-size: 0.8em;
}

.stat-label {
  font-weight: 500;
  opacity: 0.8;
}

.stat-value {
  font-weight: 600;
  color: #d79921;
}

.raw-results {
  overflow: auto;
  max-height: 200px;
  background: rgba(235, 219, 178, 0.03);
  border-radius: 4px;
  padding: 0.5rem;
}

.raw-results h3 {
  margin: 0 0 0.5rem 0;
  font-size: 0.9em;
  font-weight: 600;
}

.results-table-container {
  overflow: auto;
  max-height: 150px;
}

.results-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.8em;
}

.results-table th {
  position: sticky;
  top: 0;
  background: rgba(40, 40, 40, 0.95);
  border-bottom: 1px solid rgba(235, 219, 178, 0.2);
  padding: 0.3rem 0.4rem;
  text-align: left;
  font-weight: 600;
  font-size: 0.75em;
  color: #d79921;
}

.results-table td {
  padding: 0.3rem 0.4rem;
  border-bottom: 1px solid rgba(235, 219, 178, 0.1);
  vertical-align: top;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.results-table td:hover {
  white-space: normal;
  word-break: break-word;
}

.results-truncated {
  padding: 0.3rem;
  text-align: center;
  font-size: 0.8em;
  color: rgba(235, 219, 178, 0.6);
  font-style: italic;
}

.no-results {
  text-align: center;
  padding: 1rem;
  font-style: italic;
  color: rgba(235, 219, 178, 0.6);
}

.error-message {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  color: #fca5a5;
  padding: 0.5rem;
  border-radius: 4px;
  font-size: 0.9em;
  margin-bottom: 0.5rem;
}

.agp-section,
.agp-chart,
.agp-statistics,
.ai-recommendations-section {
  display: block !important;
  margin-top: 1rem;
  padding: 0.75rem;
  background: rgba(235, 219, 178, 0.03);
  border-radius: 6px;
}

.agp-section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.agp-section-header h3 {
  margin: 0;
  font-size: 1em;
  font-weight: 600;
}

.agp-export-buttons {
  display: flex;
  gap: 0.5rem;
}

.agp-export-button {
  background: rgba(235, 219, 178, 0.1);
  color: #ebdbb2;
  border: 1px solid rgba(235, 219, 178, 0.2);
  padding: 0.4rem 0.6rem;
  border-radius: 3px;
  font-size: 0.8em;
  cursor: pointer;
  transition: all 0.15s ease;
}

.agp-export-button:hover {
  background: rgba(235, 219, 178, 0.2);
}

.ai-recommendations-section {
  border-top: 1px solid rgba(235, 219, 178, 0.2);
  padding-top: 1rem;
}

.ai-section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.ai-section-header h3 {
  margin: 0;
  font-size: 1em;
  font-weight: 600;
}

.ai-controls {
  display: flex;
  gap: 0.5rem;
}

.refresh-ai-btn,
.toggle-ai-btn {
  background: rgba(235, 219, 178, 0.1);
  color: #ebdbb2;
  border: 1px solid rgba(235, 219, 178, 0.2);
  padding: 0.3rem 0.5rem;
  border-radius: 3px;
  font-size: 0.8em;
  cursor: pointer;
  transition: all 0.15s ease;
}

.refresh-ai-btn:hover,
.toggle-ai-btn:hover {
  background: rgba(235, 219, 178, 0.2);
}

.ai-loading {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  text-align: center;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(235, 219, 178, 0.2);
  border-top: 2px solid #d79921;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.ai-agp-recommendations h4 {
  margin: 0 0 0.75rem 0;
  font-size: 0.95em;
  font-weight: 600;
}

.recommendations-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 0.5rem;
}

.recommendation-card {
  background: rgba(235, 219, 178, 0.05);
  border-radius: 4px;
  padding: 0.6rem;
  border-left: 3px solid rgba(235, 219, 178, 0.3);
}

.recommendation-card.critical {
  border-left-color: #dc2626;
  background: rgba(220, 38, 38, 0.05);
}

.recommendation-card.high {
  border-left-color: #f59e0b;
  background: rgba(245, 158, 11, 0.05);
}

.recommendation-card.medium {
  border-left-color: #3b82f6;
  background: rgba(59, 130, 246, 0.05);
}

.recommendation-card.excellent {
  border-left-color: #10b981;
  background: rgba(16, 185, 129, 0.05);
}

.recommendation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.4rem;
}

.recommendation-category {
  font-size: 0.8em;
  font-weight: 600;
  color: #d79921;
}

.recommendation-level {
  font-size: 0.75em;
  font-weight: 500;
  padding: 0.1rem 0.3rem;
  border-radius: 8px;
  background: rgba(235, 219, 178, 0.1);
}

.recommendation-message {
  margin: 0 0 0.3rem 0;
  font-size: 0.9em;
  font-weight: 500;
  line-height: 1.4;
}

.recommendation-action {
  margin: 0 0 0.4rem 0;
  font-size: 0.85em;
  line-height: 1.4;
  opacity: 0.9;
}

.recommendation-meta {
  display: flex;
  justify-content: space-between;
  font-size: 0.75em;
  opacity: 0.7;
  font-style: italic;
}

.ai-disclaimer {
  margin-top: 0.75rem;
  padding: 0.5rem;
  background: rgba(245, 158, 11, 0.1);
  border-radius: 4px;
  border-left: 3px solid #f59e0b;
}

.ai-disclaimer p {
  margin: 0;
  font-size: 0.8em;
  line-height: 1.3;
}

.agp-settings-panel {
  overflow-y: auto;
  max-height: 100%;
  padding-right: 0.25rem;
}

.agp-settings-grid {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.setting-group {
  background: rgba(235, 219, 178, 0.05);
  padding: 0.6rem;
  border-radius: 4px;
}

.setting-group h4 {
  margin: 0 0 0.5rem 0;
  font-size: 0.9em;
  font-weight: 600;
  color: #d79921;
}

.threshold-inputs {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.5rem;
}

.threshold-input {
  display: flex;
  flex-direction: column;
  gap: 0.2rem;
}

.threshold-input label {
  font-size: 0.8em;
  font-weight: 500;
  color: rgba(235, 219, 178, 0.8);
}

.threshold-input input {
  padding: 0.3rem 0.4rem;
  border: 1px solid rgba(235, 219, 178, 0.2);
  border-radius: 3px;
  background: rgba(40, 40, 40, 0.9);
  color: #ebdbb2;
  font-size: 0.85em;
}

.unit {
  font-size: 0.8em;
  opacity: 0.7;
  margin-left: 0.3rem;
}

.performance-stats {
  background: rgba(235, 219, 178, 0.05);
  padding: 0.75rem;
  border-radius: 6px;
  margin-bottom: 0.5rem;
}

.performance-stats h3 {
  margin: 0 0 0.5rem 0;
  font-size: 0.9em;
  font-weight: 600;
}

.stat-card {
  background: rgba(235, 219, 178, 0.08);
  padding: 0.5rem;
  border-radius: 4px;
}

.stat-card h4 {
  margin: 0 0 0.2rem 0;
  font-size: 0.8em;
  font-weight: 500;
  opacity: 0.8;
}

.stat-card p {
  margin: 0;
  font-size: 0.9em;
  font-weight: 600;
  color: #d79921;
}

.stat-card.full-width {
  grid-column: 1 / -1;
}

.slow-queries {
  display: flex;
  flex-direction: column;
  gap: 0.3rem;
  margin-top: 0.5rem;
}

.slow-query {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.3rem;
  background: rgba(235, 219, 178, 0.05);
  border-radius: 3px;
  font-size: 0.8em;
}

.execution-time {
  color: #f59e0b;
  font-weight: 600;
}

.stats-actions {
  margin-top: 0.5rem;
  text-align: center;
}

.clear-stats-button {
  background: rgba(239, 68, 68, 0.8);
  color: white;
  border: none;
  padding: 0.4rem 0.8rem;
  border-radius: 3px;
  font-size: 0.8em;
  cursor: pointer;
  transition: background 0.15s ease;
}

.clear-stats-button:hover {
  background: rgba(239, 68, 68, 0.9);
}

.stats-button,
.connect-button {
  background: rgba(235, 219, 178, 0.1);
  color: #ebdbb2;
  border: 1px solid rgba(235, 219, 178, 0.2);
  padding: 0.4rem 0.6rem;
  border-radius: 3px;
  font-size: 0.8em;
  cursor: pointer;
  transition: all 0.15s ease;
}

.stats-button:hover,
.connect-button:hover:not(:disabled) {
  background: rgba(235, 219, 178, 0.2);
}

.connect-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Compact mode styles */
.query-runner.compact {
  font-size: 0.85em;
  gap: 0.375rem;
}

.query-runner.compact .query-runner-header {
  padding: 0.375rem 0;
}

.query-runner.compact .query-runner-header h2 {
  font-size: 1rem;
}

.query-runner.compact .tabs {
  padding: 0.25rem;
}

.query-runner.compact .tab-button {
  padding: 0.375rem 0.75rem;
  font-size: 0.8rem;
}

.query-runner.compact .tab-content {
  padding: 0.75rem;
  max-height: calc(100vh - 250px);
}

.query-runner.compact .query-form {
  gap: 0.75rem;
}

.query-runner.compact .form-group {
  gap: 0.375rem;
}

.query-runner.compact .form-group label {
  font-size: 0.8rem;
}

.query-runner.compact .form-group select,
.query-runner.compact .form-group input,
.query-runner.compact .form-group textarea {
  padding: 0.375rem 1.5rem 0.375rem 0.5rem;
  font-size: 0.8rem;
  min-height: 32px;
}

.query-runner.compact .parameter-input select {
  background-size: 0.75rem;
  background-position: right 0.375rem center;
}

.query-runner.compact .parameter-input input {
  padding: 0.375rem 0.5rem;
}

.query-runner.compact .results-section {
  max-height: 300px;
}

.query-runner.compact .results-table {
  font-size: 0.75rem;
}

.query-runner.compact .results-table th,
.query-runner.compact .results-table td {
  padding: 0.25rem 0.5rem;
}

.query-runner.compact .agp-chart-container {
  height: 200px;
}

.query-runner.compact .stats-grid {
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 0.5rem;
}

.query-runner.compact .stat-card {
  padding: 0.5rem;
}

.query-runner.compact .stat-card h4 {
  font-size: 0.75rem;
}

.query-runner.compact .stat-card p {
  font-size: 0.9rem;
}
