# Database Scripts

Scripts for database setup, population, and data management.

## Scripts

- **populateDatabase.js** - Main database population script
- **populateD1NAMODatabase.js** - D1NAMO dataset population
- **populateNeo4jDatabase.js** - Neo4j database setup
- **importD1NAMODataset.js** - Import D1NAMO research data
- **importRealD1NAMODataset.js** - Import real D1NAMO dataset
- **verifyImportedData.js** - Verify data integrity after import

## Usage

```bash
# Populate main database
npm run populate-db

# Run specific database script
node scripts/database/populateD1NAMODatabase.js
```
