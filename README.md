# HealthHub Research Platform

A modern React-based healthcare analytics platform with Neo4j Graph Database and AI-Powered Clinical Insights, specifically focused on Natural Language Generation (NLG) research for healthcare and clinical documentation applications.

## 🚀 Quick Deploy

This project includes **automated CI/CD deployment to Cloudflare Pages**:

1. **Setup**: Configure Cloudflare API credentials in your deployment environment
2. **Deploy**: Push to `main` branch - automatic deployment via GitHub Actions
3. **Access**: Your app will be live at `https://healthhub-research-platform.pages.dev`

## 📋 Features

- **🏥 Healthcare Analytics**: AGP (Ambulatory Glucose Profile) analysis and visualization
- **🧠 AI Clinical Insights**: AI-powered recommendations and clinical analysis
- **📊 Neo4j Graph Database**: Patient data relationships and complex healthcare queries
- **🔒 Secure Backend**: Cloudflare Functions with rate limiting and data validation
- **📱 Responsive Design**: Works on desktop and mobile devices
- **⚡ Real-time**: Live data updates and interactive visualizations
- **👤 User Authentication**: Secure login system for researchers
- **📈 Dashboard Overview**: Comprehensive view of research projects and statistics
- **🔬 Research Tools**: Integrated tools for data analysis, NLG models, and clinical documentation
- **📋 Project Management**: Track active studies, completed analyses, and publications

## 🛠️ Local Development

Local dev runs the Vite frontend and Cloudflare Pages Functions locally, so all features work without external services.

### Prerequisites

- Node.js (version 14 or higher)
- npm or yarn package manager

### Setup (Windows PowerShell):

```powershell
# 1) Install deps
npm install

# 2) Create env files
Copy-Item .env.example .env -Force   # Vite-only vars (non-secret)
Copy-Item .dev.vars.example .dev.vars -Force   # Wrangler server secrets (NOT committed)

# 3) Start local dev (runs Vite + Wrangler)
npm run dev:local
```

### Alternative Setup (Cross-platform):

```bash
# 1) Clone the repository
git clone https://github.com/AlishaMR-123/healthhub-research-platform.git
cd healthhub-research-platform

# 2) Install dependencies
npm install

# 3) Start the development server
npm run dev
```

### What this does:
- Vite dev server at http://localhost:5173
- Wrangler serves Pages Functions at http://127.0.0.1:8787
- Vite proxies /api/* to Wrangler, so endpoints like /api/neo4j and /api/ai work locally

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint
- `npm run dev:local` - Start local dev with Wrangler

### Notes:
- Put server-only secrets (NEO4J_*, OPENAI_API_KEY) in .dev.vars for local; use Wrangler secrets in production.
- Keep frontend .env variables prefixed with VITE_. Avoid placing secrets there.

## 🧪 Backend API

The application includes secure Cloudflare Functions for:

- **Neo4j Operations**: `/api/neo4j` - Secure database queries with validation
- **AI Services**: `/api/ai` - Clinical insights and recommendations
- **Health Check**: `/api/health` - System status monitoring
- **API Documentation**: `/api` - Complete endpoint documentation

## 🚀 Deploying to Production (Cloudflare Pages)

Two ways to deploy:

1) Local script (fastest)
- Requires Cloudflare credentials already configured locally or via Pages project
- Run: PowerShell script `./deploy.ps1` builds, commits, pushes, and deploys
- Output includes preview and production URLs plus API health check

2) CI/CD (hands‑free)
- Push to `main` → GitHub Actions builds and deploys automatically
- Configure GitHub Secrets: CLOUDFLARE_API_TOKEN, CLOUDFLARE_ACCOUNT_ID
- Build settings in Pages: build command `npm run build`, output `dist/`

Use .env in CI:
- Place non-sensitive defaults in `.env` (checked in or provided by your team)
- Keep secrets in GitHub Secrets. The workflow loads `.env` if present and overlays secrets
- Key secrets: `VITE_NEO4J_*` for browser, `NEO4J_*` for serverless functions

Troubleshooting:
- If deploy fails locally, ensure Wrangler is logged in and `wrangler.toml` has your account/project
- For Neo4j in the browser, prefer API proxy: set `VITE_NEO4J_USE_API=true` and configure Pages secrets for server-side Neo4j creds

## Cloudflare Workers Support

This project supports Cloudflare Workers for serverless API routes or edge logic.

- Place Worker functions in the `/functions` directory (see `functions/hello.js` for an example).
- `wrangler.toml` is included for local development and advanced deployment.
- To test locally: `npx wrangler dev`

For more, see the [Cloudflare Workers docs](https://developers.cloudflare.com/workers/).

This project is ready for static deployment on Cloudflare Pages.

## Build Output
- The production build is output to the `dist/` folder (default for Vite).

## Routing
- SPA routing is handled by `public/_redirects`:
  ```
  /*    /index.html   200
  ```
- This ensures all routes serve the SPA entry point.

## Environment Variables

Create a local .env from .env.example and fill in your values. Do not commit .env.

- Copy `.env.example` to `.env` and fill in your values.
- Do NOT commit `.env` files (already gitignored).
- Frontend variables must be prefixed with `VITE_` and should not contain real credentials.
- Server-side secrets (Cloudflare Pages Functions) must be set with `wrangler pages secret put`:
   - `NEO4J_URI`, `NEO4J_USERNAME`, `NEO4J_PASSWORD`, `NEO4J_DATABASE`
   - `OPENAI_API_KEY`

Recommended: Use the Neo4j API proxy in the browser by setting `VITE_NEO4J_USE_API=true`. This keeps credentials server-side and avoids SSL issues when developing over HTTP.

## .gitignore
- Ignores build, node_modules, .env, Cloudflare, and local config files.

Note: If any secrets were accidentally committed previously (e.g., inside built `dist` assets), rotate those credentials immediately and consider purging history.

## CI/CD Deployment
This project uses GitHub Actions for automated deployment to Cloudflare Pages:

- **Automatic deployment** on push to `main` branch
- **Pull request previews** for testing changes
- **Cloudflare Workers** support for serverless functions
- **Environment variable** injection from GitHub Secrets

### Quick Setup
1. Add Cloudflare API token and Account ID to GitHub Secrets
2. Push to `main` branch to trigger deployment
3. Your site will be live at your Cloudflare Pages URL

For detailed setup instructions, see [`DEPLOY.md`](./DEPLOY.md).

## Ongoing Maintenance

- Run `npm run lint` and address warnings incrementally (hooks deps, unused vars).
- Keep dependencies current with `npm outdated` and apply safe patch updates.
- Prefer server-side access to databases and AI APIs; the frontend should call `/api/*` endpoints.
- Review branch protection rules and PR templates to maintain quality.

---

For more, see the [Cloudflare Pages docs](https://developers.cloudflare.com/pages/).

---

For more information, see the [Cloudflare Pages docs](https://developers.cloudflare.com/pages/).

## 💻 Technology Stack

- **Frontend**: React 19 with Vite
- **Styling**: Modern CSS with gradients and animations
- **Icons**: Emoji-based iconography for better UX
- **Responsive Design**: Mobile-friendly interface
- **Backend**: Cloudflare Functions with Neo4j and OpenAI integration
- **Database**: Neo4j Graph Database for complex healthcare data relationships
- **AI Services**: OpenAI integration for clinical insights and NLG capabilities

## 📋 Usage

### Login
- Use any email and password to access the demo platform
- The system simulates authentication for demonstration purposes

### Dashboard Features

#### Overview Section
- View project statistics and metrics
- Monitor recent project progress
- Track completion percentages

#### Research Tools
- **Data Analysis**: Advanced analytics and visualization tools
- **NLG Models**: Natural Language Generation testing environment
- **Clinical Documentation**: Healthcare documentation generation tools
- **Text Analysis**: Medical text processing and analysis

#### Settings
- Configure research preferences
- Manage notification settings
- Customize user experience

### Demo Credentials

Since this is a demo application, you can use any email and password combination to log in and explore the features.

## 📁 Project Structure

```
src/
├── components/           # Reusable UI components
│   ├── Login.jsx        # Login component with form
│   ├── Dashboard.jsx    # Main dashboard component
│   └── *.css           # Component styling
├── features/            # Feature-specific modules
│   ├── neo4j/          # Neo4j database integration
│   ├── ai/             # AI services and NLG tools
│   └── analytics/      # Healthcare analytics
├── services/           # API services and utilities
├── hooks/              # Custom React hooks
├── utils/              # Utility functions
├── constants/          # Application constants
├── theme/              # Styling and theme configuration
├── assets/             # Static assets
├── App.jsx             # Main application component
├── main.jsx            # React entry point
└── index.css           # Global styles
```

## 🔧 Development

The application is built with modern React patterns and includes:

- Functional components with hooks
- State management using useState
- Responsive CSS Grid and Flexbox layouts
- Component-based architecture
- Modern ES6+ JavaScript

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 🔄 Ongoing Maintenance

- Run `npm run lint` and address warnings incrementally (hooks deps, unused vars).
- Keep dependencies current with `npm outdated` and apply safe patch updates.
- Prefer server-side access to databases and AI APIs; the frontend should call `/api/*` endpoints.
- Review branch protection rules and PR templates to maintain quality.

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- Built with React and Vite for optimal development experience
- Designed for healthcare and medical research applications
- Focused on Natural Language Generation research workflows
- Powered by Neo4j Graph Database and OpenAI for advanced analytics

## Neo4j service: real vs mock

The app can operate with or without a live Neo4j instance.

- If the `neo4j-driver` package is available and Vite env vars are set, the app will connect to Neo4j.
- If the driver is unavailable or a connection can’t be established, the app automatically falls back to mock data for demos and development.

Environment variables (Vite):

- `VITE_NEO4J_URI` (e.g., `bolt://localhost:7687`)
- `VITE_NEO4J_USERNAME`
- `VITE_NEO4J_PASSWORD`
   - Also set `NEO4J_PASSWORD` as a Cloudflare secret for server functions.
- `VITE_NEO4J_DATABASE` (default: `neo4j`)

Tip: For pure frontend demos, you don’t need to install the Neo4j driver—mock mode will populate charts and views. See `src/features/neo4j/services/neo4jService.js` for behavior and `src/features/neo4j/utils/` for AGP helpers.

---

For more information, see the [Cloudflare Pages docs](https://developers.cloudflare.com/pages/).
