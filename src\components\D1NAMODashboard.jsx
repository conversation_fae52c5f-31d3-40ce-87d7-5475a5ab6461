import { useEffect, useMemo, useState } from 'react';
import neo4jService from '../services/neo4jService';
import { mapPatientData, mapSynchronizedData, batchMapData, validateRequiredFields } from '../utils/neo4jSchemaMapper';
import './D1NAMODashboard.css';
import ECGGlucoseCorrelation from './ECGGlucoseCorrelation';
import ECGWaveformViewer from './ECGWaveformViewer';
import PatientHealthRecommendations from './PatientHealthRecommendations';
import PromptTesting from './PromptTesting';

const D1NAMODashboard = ({ compact = false }) => {
    const [selectedPatient, setSelectedPatient] = useState(null);
    const [patients, setPatients] = useState([]);
    const [synchronizedData, setSynchronizedData] = useState([]);
    const [allPatientData, setAllPatientData] = useState([]); // Store all data for filtering
    const [ecgLeadData, setECGLeadData] = useState([]);
    const [selectedTimestamp, setSelectedTimestamp] = useState(null);
    const [selectedLead, setSelectedLead] = useState('II');
    const [timeRange, setTimeRange] = useState('all'); // Changed to string identifier
    const [availableTimeRanges, setAvailableTimeRanges] = useState([]); // Dynamic time ranges
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [keyboardNavigation, setKeyboardNavigation] = useState(false);
    const [activeView, setActiveView] = useState('correlation'); // correlation | recommendations | promptTesting | analytics
    const [promptTestHistory, setPromptTestHistory] = useState([]);
    const [dataSummary, setDataSummary] = useState(null);
    const [patientDataQuality, setPatientDataQuality] = useState(null);

    // Initialize Neo4j connection and load patients on component mount
    useEffect(() => {
        initializeConnection();
    }, []);

    // Keyboard navigation support
    useEffect(() => {
        const handleKeyDown = (event) => {
            // Enable keyboard navigation mode when Tab is pressed
            if (event.key === 'Tab') {
                setKeyboardNavigation(true);
            }

            // Quick keyboard shortcuts
            if (event.ctrlKey || event.metaKey) {
                switch (event.key) {
                    case 'r':
                        event.preventDefault();
                        if (!loading) {
                            loadD1NAMOPatients();
                        }
                        break;
                    case 'e':
                        event.preventDefault();
                        if (selectedPatient && synchronizedData.length > 0 && !loading) {
                            handleExportData();
                        }
                        break;
                    case '1':
                        event.preventDefault();
                        setActiveView('correlation');
                        break;
                    case '2':
                        event.preventDefault();
                        setActiveView('recommendations');
                        break;
                    case '3':
                        event.preventDefault();
                        setActiveView('promptTesting');
                        break;
                    case '4':
                        event.preventDefault();
                        setActiveView('analytics');
                        break;
                }
            }
        };

        const handleMouseDown = () => {
            setKeyboardNavigation(false);
        };

        document.addEventListener('keydown', handleKeyDown);
        document.addEventListener('mousedown', handleMouseDown);

        return () => {
            document.removeEventListener('keydown', handleKeyDown);
            document.removeEventListener('mousedown', handleMouseDown);
        };
    }, [loading, selectedPatient, synchronizedData.length]);

    const initializeConnection = async () => {
        try {
            setLoading(true);
            setError(null);

            // Connect to Neo4j if not already connected
            if (!neo4jService.isConnected) {
                await neo4jService.connect();
            }

            // Load D1NAMO data summary first
            const summaryResult = await neo4jService.getD1NAMODataSummary();
            if (summaryResult.success && summaryResult.records.length > 0) {
                setDataSummary(summaryResult.records[0]);
            }

            // Load patients after successful connection
            await loadD1NAMOPatients();
        } catch (err) {
            console.error('Error initializing Neo4j connection:', err);
            setError(`Failed to connect to database: ${err.message}`);
        } finally {
            setLoading(false);
        }
    };

    // Load synchronized data when patient changes
    useEffect(() => {
        if (selectedPatient) {
            loadAllSynchronizedData();
        }
    }, [selectedPatient]);

    // Filter data when time range changes
    useEffect(() => {
        if (allPatientData.length > 0) {
            filterDataByTimeRange();
        }
    }, [timeRange, allPatientData]);

    // Load prompt comparison history for analytics when dashboard mounts or patient changes
    useEffect(() => {
        try {
            const history = localStorage.getItem('promptComparisonHistory');
            if (history) {
                setPromptTestHistory(JSON.parse(history));
            } else {
                setPromptTestHistory([]);
            }
        } catch (e) {
            setPromptTestHistory([]);
        }
    }, [selectedPatient]);

    const loadD1NAMOPatients = async () => {
        try {
            console.log('Loading D1NAMO patients...');
            setError(null); // Clear any previous errors

            const result = await neo4jService.getD1NAMOPatients();

            if (result.success && result.records) {
                // Validate and process patient data using schema mapper
                const mappedPatients = batchMapData(result.records, mapPatientData);

                const validPatients = mappedPatients.filter(patient => {
                    const validation = validateRequiredFields(patient, ['patientId']);
                    if (!validation.isValid) {
                        console.warn('Patient validation failed:', validation.errors, patient);
                        return false;
                    }
                    return true;
                });

                setPatients(validPatients);
                console.log(`Loaded ${validPatients.length} D1NAMO patients with schema mapping`);

                // Auto-select first patient if available and none currently selected
                if (validPatients.length > 0 && !selectedPatient) {
                    setSelectedPatient(validPatients[0]);
                }

                // Show success message if patients were loaded
                if (validPatients.length > 0) {
                    console.log(`✅ Successfully loaded ${validPatients.length} patients from D1NAMO dataset`);
                }
            } else {
                console.warn('No D1NAMO patients found in database');
                setPatients([]);
                setError('No D1NAMO patients found in the database. Please ensure the D1NAMO dataset has been imported.');
            }
        } catch (err) {
            console.error('Error loading D1NAMO patients:', err);

            // Provide more specific error messages
            let errorMessage = 'Failed to load patient data. ';
            if (err.message.includes('connection')) {
                errorMessage += 'Please check your Neo4j database connection.';
            } else if (err.message.includes('authentication')) {
                errorMessage += 'Database authentication failed. Please check your credentials.';
            } else if (err.message.includes('timeout')) {
                errorMessage += 'Database query timed out. Please try again.';
            } else {
                errorMessage += `Error: ${err.message}`;
            }

            setError(errorMessage);
            setPatients([]);
        }
    };

    // Helper function to calculate available time ranges based on actual data
    const calculateAvailableTimeRanges = (data) => {
        if (!data || data.length === 0) return [];

        const timestamps = data
            .map(d => new Date(d.timestamp))
            .filter(d => !isNaN(d.getTime()))
            .sort((a, b) => a - b);

        if (timestamps.length === 0) return [];

        const earliest = timestamps[0];
        const latest = timestamps[timestamps.length - 1];
        const totalSpanMs = latest.getTime() - earliest.getTime();
        const totalSpanDays = totalSpanMs / (24 * 60 * 60 * 1000);

        const ranges = [
            { id: 'all', label: 'All available data', daysFromLatest: null }
        ];

        // Add dynamic ranges based on actual data span
        if (totalSpanDays >= 1) {
            ranges.push({ id: 'last_24h', label: 'Last 24 hours', daysFromLatest: 1 });
        }
        if (totalSpanDays >= 3) {
            ranges.push({ id: 'last_3d', label: 'Last 3 days', daysFromLatest: 3 });
        }
        if (totalSpanDays >= 7) {
            ranges.push({ id: 'last_7d', label: 'Last week', daysFromLatest: 7 });
        }
        if (totalSpanDays >= 14) {
            ranges.push({ id: 'last_14d', label: 'Last 2 weeks', daysFromLatest: 14 });
        }
        if (totalSpanDays >= 30) {
            ranges.push({ id: 'last_30d', label: 'Last month', daysFromLatest: 30 });
        }

        // Note: Additional ranges for the actual data span could be added here if needed
        // based on totalSpanDays (days/weeks/months formatting was removed as unused)

        return ranges;
    };

    // Helper function to filter data based on selected time range
    const filterDataByTimeRange = () => {
        if (!allPatientData.length) {
            setSynchronizedData([]);
            return;
        }

        if (timeRange === 'all') {
            setSynchronizedData(allPatientData);
            return;
        }

        // Backward compatibility: handle numeric timeRange (legacy format)
        if (typeof timeRange === 'number' || !isNaN(parseInt(timeRange))) {
            const daysFromLatest = typeof timeRange === 'number' ? timeRange : parseInt(timeRange);
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - daysFromLatest);

            const filtered = allPatientData.filter(d => {
                const dataDate = new Date(d.timestamp);
                return dataDate >= cutoffDate;
            });

            setSynchronizedData(filtered);
            return;
        }

        // New string identifier format
        const selectedRange = availableTimeRanges.find(r => r.id === timeRange);
        if (!selectedRange || !selectedRange.daysFromLatest) {
            setSynchronizedData(allPatientData);
            return;
        }

        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - selectedRange.daysFromLatest);

        const filtered = allPatientData.filter(d => {
            const dataDate = new Date(d.timestamp);
            return dataDate >= cutoffDate;
        });

        setSynchronizedData(filtered);
        console.log(`Filtered to ${filtered.length} points from last ${selectedRange.daysFromLatest} days`);
    };

    // Load all synchronized data for the selected patient
    const loadAllSynchronizedData = async () => {
        if (!selectedPatient?.patientId) return;

        try {
            setLoading(true);
            setError(null);

            // Load all data without time restriction
            const result = await neo4jService.getSynchronizedECGGlucoseData(
                selectedPatient.patientId,
                365, // Load up to 1 year of data to get the full span
                10000 // Increased limit to get all available data
            );

            if (result.success && result.records) {
                // Normalize record shape to what visualization components expect
                const normalized = result.records.map((r) => ({
                    // Prefer glucose timestamp, fall back to ecg timestamp
                    timestamp: r.glucoseTimestamp || r.ecgTimestamp || r.timestamp,
                    // Standardize glucose key
                    glucose: typeof r.glucoseValue === 'number' ? r.glucoseValue : (
                        typeof r.glucose === 'number' ? r.glucose : undefined
                    ),
                    // Standardize ECG feature keys
                    heartRate: r.heartRate ?? r.heart_rate ?? undefined,
                    hrv: r.hrv ?? r.hrv_rmssd ?? undefined,
                    qtcInterval: r.qtcInterval ?? r.qtc ?? r.qtc_interval ?? undefined,
                    rrInterval: r.rrInterval ?? r.rr_interval ?? undefined,
                    // Carry original ids if present
                    ecgId: r.ecgId,
                    glucoseId: r.glucoseId,
                })).filter(d => d.timestamp);

                // Store all data and calculate available time ranges
                setAllPatientData(normalized);
                const ranges = calculateAvailableTimeRanges(normalized);
                setAvailableTimeRanges(ranges);

                // Set initial time range to 'all' for new patient
                setTimeRange('all');
                setSynchronizedData(normalized);

                console.log(`Loaded ${result.records.length} synchronized rows, ${normalized.length} normalized points`);
            } else {
                setAllPatientData([]);
                setSynchronizedData([]);
                setAvailableTimeRanges([]);
            }
        } catch (err) {
            console.error('Error loading synchronized data:', err);
            setError('Failed to load synchronized data.');
            setAllPatientData([]);
            setSynchronizedData([]);
            setAvailableTimeRanges([]);
        } finally {
            setLoading(false);
        }
    };

    const loadECGLeadData = async (timestamp) => {
        if (!selectedPatient?.patientId || !timestamp) return;

        try {
            setLoading(true);
            const result = await neo4jService.getECGLeadData(selectedPatient.patientId, timestamp, selectedLead);

            if (result.success && result.records) {
                setECGLeadData(result.records);
            } else {
                setECGLeadData([]);
            }
        } catch (err) {
            console.error('Error loading ECG lead data:', err);
            setECGLeadData([]);
        } finally {
            setLoading(false);
        }
    };

    // Handle patient selection
    const handlePatientChange = async (patient) => {
        setSelectedPatient(patient);
        setSynchronizedData([]);
        setECGLeadData([]);
        setSelectedTimestamp(null);
        setPatientDataQuality(null);

        if (patient?.patientId) {
            try {
                const qualityResult = await neo4jService.getPatientDataQuality(patient.patientId);
                if (qualityResult.success && qualityResult.records.length > 0) {
                    setPatientDataQuality(qualityResult.records[0]);
                }
            } catch (err) {
                console.warn('Could not load patient data quality:', err.message);
            }
        }
    };

    // Handle time range change
    const handleTimeRangeChange = (rangeId) => {
        // Handle both legacy numeric format and new string identifier format
        setTimeRange(rangeId);
    };

    // Handle data export
    const handleExportData = () => {
        if (!selectedPatient || !synchronizedData.length) return;

        try {
            const exportData = {
                patient: selectedPatient,
                synchronizedData,
                ecgLeadData,
                timeRange,
                exportDate: new Date().toISOString(),
                dataQuality: patientDataQuality
            };

            const dataStr = JSON.stringify(exportData, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            const url = URL.createObjectURL(dataBlob);

            const link = document.createElement('a');
            link.href = url;
            link.download = `d1namo-data-${selectedPatient.patientId}-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
        } catch (error) {
            console.error('Export failed:', error);
            alert('Failed to export data. Please try again.');
        }
    };

    // Handle clear data
    const handleClearData = () => {
        if (window.confirm('Are you sure you want to clear all loaded data? This will not affect the database.')) {
            setSynchronizedData([]);
            setECGLeadData([]);
            setSelectedTimestamp(null);
            setPatientDataQuality(null);
            setTimeRange('all');
            setAvailableTimeRanges([]);
        }
    };

    // Handle ECG data point click for detailed view
    const handleDataPointClick = (dataPoint) => {
        if (dataPoint?.timestamp) {
            setSelectedTimestamp(dataPoint.timestamp);
            loadECGLeadData(dataPoint.timestamp);
        }
    };

    // Handle lead change for ECG viewer
    const handleLeadChange = (leadName) => {
        setSelectedLead(leadName);
    };

    // Calculate dashboard statistics with enhanced D1NAMO-specific metrics
    const dashboardStats = useMemo(() => {
        if (!synchronizedData?.length) return {};

        const glucoseValues = synchronizedData
            .filter(d => d.glucose && !isNaN(d.glucose))
            .map(d => d.glucose);

        const heartRateValues = synchronizedData
            .filter(d => d.heartRate && !isNaN(d.heartRate))
            .map(d => d.heartRate);

        const hrvValues = synchronizedData
            .filter(d => d.hrv && !isNaN(d.hrv))
            .map(d => d.hrv);

        const syncQualityValues = synchronizedData
            .filter(d => d.syncTimeDiff !== undefined)
            .map(d => d.syncTimeDiff || 0);

        // Enhanced D1NAMO-specific metrics for aggregated data
        const perfectSyncCount = syncQualityValues.filter(t => t === 0).length;
        const goodSyncCount = syncQualityValues.filter(t => t <= 12).length; // 12 hours for daily data
        const avgSyncTime = syncQualityValues.length > 0 ?
            syncQualityValues.reduce((a, b) => a + b, 0) / syncQualityValues.length : 0;

        // Calculate total original ECG readings from aggregated data
        const totalOriginalReadings = synchronizedData
            .filter(d => d.originalReadingCount && !isNaN(d.originalReadingCount))
            .reduce((sum, d) => sum + d.originalReadingCount, 0);

        // Calculate compression ratio
        const compressionRatio = totalOriginalReadings > 0 ?
            Math.round(totalOriginalReadings / synchronizedData.length) : 0;

        return {
            totalReadings: synchronizedData.length,
            totalOriginalReadings: totalOriginalReadings.toLocaleString(),
            compressionRatio: compressionRatio.toLocaleString() + ':1',
            avgGlucose: glucoseValues.length > 0 ?
                (glucoseValues.reduce((a, b) => a + b, 0) / glucoseValues.length).toFixed(1) : 'N/A',
            avgHeartRate: heartRateValues.length > 0 ?
                (heartRateValues.reduce((a, b) => a + b, 0) / heartRateValues.length).toFixed(1) : 'N/A',
            avgHRV: hrvValues.length > 0 ?
                (hrvValues.reduce((a, b) => a + b, 0) / hrvValues.length).toFixed(1) : 'N/A',
            timeInRange: glucoseValues.length > 0 ?
                ((glucoseValues.filter(g => g >= 70 && g <= 180).length / glucoseValues.length) * 100).toFixed(1) : 'N/A',
            // D1NAMO-specific synchronization metrics for daily aggregated data
            perfectSyncPercent: syncQualityValues.length > 0 ?
                ((perfectSyncCount / syncQualityValues.length) * 100).toFixed(1) : 'N/A',
            goodSyncPercent: syncQualityValues.length > 0 ?
                ((goodSyncCount / syncQualityValues.length) * 100).toFixed(1) : 'N/A',
            avgSyncTime: avgSyncTime.toFixed(1),
            dataQuality: avgSyncTime <= 6 ? 'Excellent' : avgSyncTime <= 12 ? 'Good' : avgSyncTime <= 24 ? 'Fair' : 'Poor',
            ecgFeatureCount: synchronizedData.filter(d => d.heartRate || d.hrv || d.ecgMean).length,
            glucoseDataPoints: glucoseValues.length,
            ecgDataPoints: heartRateValues.length
        };
    }, [synchronizedData]);

    // Build a lightweight patientData object for AI components
    const patientData = useMemo(() => {
        if (!selectedPatient) return null;

        const glucoseValues = synchronizedData
            ?.filter(d => typeof d.glucose === 'number')
            ?.map(d => d.glucose) || [];

        const avgGlucose = glucoseValues.length
            ? glucoseValues.reduce((a, b) => a + b, 0) / glucoseValues.length
            : undefined;

        const timeInRange = glucoseValues.length
            ? (glucoseValues.filter(g => g >= 70 && g <= 180).length / glucoseValues.length) * 100
            : undefined;

        return {
            name: selectedPatient.name,
            age: selectedPatient.age,
            gender: selectedPatient.gender,
            diabetesType: selectedPatient.condition, // map condition -> diabetesType
            diagnosisDate: selectedPatient.diagnosisDate,
            currentMedications: selectedPatient.medications || [],
            recentGlucoseData: {
                averageGlucose: avgGlucose,
                timeInRange: timeInRange ? Number(timeInRange.toFixed(1)) : undefined,
            },
            lifestyle: selectedPatient.lifestyle || {},
            challenges: selectedPatient.challenges || [],
            goals: selectedPatient.goals || [],
        };
    }, [selectedPatient, synchronizedData]);

    const handlePromptResultsUpdate = () => {
        // Refresh local history after a comparison run finishes
        try {
            const history = localStorage.getItem('promptComparisonHistory');
            setPromptTestHistory(history ? JSON.parse(history) : []);
        } catch {
            setPromptTestHistory([]);
        }
    };

    return (
        <div className={`d1namo-dashboard ${compact ? 'compact' : ''} ${keyboardNavigation ? 'keyboard-navigation' : ''}`}>
            {/* Keyboard shortcuts help */}
            {keyboardNavigation && (
                <div className="keyboard-shortcuts-help" role="dialog" aria-label="Keyboard shortcuts">
                    <div className="shortcuts-content">
                        <h4>⌨️ Keyboard Shortcuts</h4>
                        <div className="shortcuts-grid">
                            <div className="shortcut-item">
                                <kbd>Ctrl+R</kbd>
                                <span>Refresh patients</span>
                            </div>
                            <div className="shortcut-item">
                                <kbd>Ctrl+E</kbd>
                                <span>Export data</span>
                            </div>
                            <div className="shortcut-item">
                                <kbd>Ctrl+1</kbd>
                                <span>Correlation view</span>
                            </div>
                            <div className="shortcut-item">
                                <kbd>Ctrl+2</kbd>
                                <span>AI Insights</span>
                            </div>
                            <div className="shortcut-item">
                                <kbd>Ctrl+3</kbd>
                                <span>Prompt Lab</span>
                            </div>
                            <div className="shortcut-item">
                                <kbd>Ctrl+4</kbd>
                                <span>Analytics</span>
                            </div>
                        </div>
                        <p className="shortcuts-note">Press <kbd>Tab</kbd> to navigate, <kbd>Enter</kbd> to activate</p>
                    </div>
                </div>
            )}

            <div className="dashboard-header">
                <div className="header-content">
                    <h1>D1NAMO Research Dashboard</h1>
                    <p>Synchronized ECG and Continuous Glucose Monitoring Analysis</p>

                    {/* User flow guidance */}
                    <div className="workflow-guide">
                        <div className="workflow-steps">
                            <div className={`workflow-step ${!selectedPatient ? 'active' : 'completed'}`}>
                                <span className="step-number">1</span>
                                <span className="step-text">Select Patient</span>
                                {!selectedPatient && <span className="step-arrow">👆</span>}
                            </div>
                            <div className={`workflow-step ${selectedPatient && !synchronizedData.length ? 'active' : selectedPatient && synchronizedData.length ? 'completed' : 'pending'}`}>
                                <span className="step-number">2</span>
                                <span className="step-text">Load Data</span>
                                {selectedPatient && !synchronizedData.length && <span className="step-arrow">⏳</span>}
                            </div>
                            <div className={`workflow-step ${synchronizedData.length > 0 ? 'active' : 'pending'}`}>
                                <span className="step-number">3</span>
                                <span className="step-text">Analyze Results</span>
                                {synchronizedData.length > 0 && <span className="step-arrow">📊</span>}
                            </div>
                        </div>

                        {/* Dashboard status indicator */}
                        <div className="dashboard-status">
                            <div className="status-indicator">
                                <span className={`status-dot ${
                                    error ? 'error' :
                                    loading ? 'loading' :
                                    synchronizedData.length > 0 ? 'success' :
                                    selectedPatient ? 'warning' : 'idle'
                                }`}></span>
                                <span className="status-text">
                                    {error ? 'Error - Check connection' :
                                     loading ? 'Loading data...' :
                                     synchronizedData.length > 0 ? `Ready - ${synchronizedData.length} data points loaded` :
                                     selectedPatient ? 'Patient selected - Load data to continue' :
                                     `${patients.length} patients available`}
                                </span>
                            </div>
                            {keyboardNavigation && (
                                <div className="keyboard-indicator">
                                    <span>⌨️ Keyboard mode active</span>
                                </div>
                            )}
                        </div>
                    </div>
                </div>

                <div className="header-controls">
                    <div className="patient-selector">
                        <label htmlFor="patient-select">
                            Research Subject:
                            <span className="label-description">Choose a patient to analyze their synchronized ECG and glucose data</span>
                        </label>
                        <select
                            id="patient-select"
                            value={selectedPatient?.patientId || ''}
                            onChange={(e) => {
                                const patient = patients.find(p => p.patientId === e.target.value);
                                if (patient) handlePatientChange(patient);
                            }}
                            disabled={loading || !patients.length}
                            title="Select a research subject to view their synchronized ECG and continuous glucose monitoring data. Green indicates complete data, yellow indicates partial data, red indicates missing data."
                            aria-describedby="patient-select-help"
                        >
                            <option value="">Select a patient...</option>
                            {patients.map(patient => {
                                const dataIndicator = patient.dataAvailability === 'complete' ? '🟢' :
                                    patient.dataAvailability === 'partial' ? '🟡' : '🔴';

                                // Fix data structure mismatch - use correct field names from Neo4j with proper null checks
                                const ecgCount = Number(patient.dailyECGSummaryCount || patient.ecgReadingCount || 0);
                                const glucoseCount = Number(patient.glucoseReadingCount || 0);
                                const totalOriginalECG = Number(patient.totalOriginalECGReadings || 0);

                                // Validate patient data structure
                                if (!patient.patientId) {
                                    console.warn('Patient missing patientId:', patient);
                                    return null;
                                }

                                return (
                                    <option key={patient.patientId} value={patient.patientId}>
                                        {dataIndicator} {patient.name || `Patient ${patient.patientId}`} ({patient.patientId}) - {ecgCount} ECG Days, {glucoseCount} Glucose Readings
                                        {totalOriginalECG > 0 && ` (${totalOriginalECG.toLocaleString()} original ECG samples)`}
                                    </option>
                                );
                            }).filter(Boolean)}
                        </select>
                        <div id="patient-select-help" className="help-text">
                            {!selectedPatient && !loading && patients.length === 0 && (
                                <span className="help-message warning">
                                    📊 No patients found. Please check your database connection or populate the D1NAMO dataset.
                                </span>
                            )}
                            {loading && (
                                <span className="help-message loading">
                                    ⏳ Loading patient data from Neo4j database...
                                </span>
                            )}
                            {!selectedPatient && patients.length > 0 && !loading && (
                                <span className="help-message info">
                                    👆 Select a patient above to begin analyzing their synchronized ECG and glucose data
                                </span>
                            )}
                        </div>
                        {selectedPatient?.dataAvailability && (
                            <div className="data-availability-info">
                                <span className={`availability-badge ${selectedPatient.dataAvailability}`}
                                      title={`Data completeness: ${selectedPatient.dataAvailability === 'complete' ? 'Both ECG and glucose data available' :
                                              selectedPatient.dataAvailability === 'partial' ? 'Either ECG or glucose data missing' :
                                              'No synchronized data available'}`}>
                                    {selectedPatient.dataAvailability === 'complete' && '🟢 Complete Dataset'}
                                    {selectedPatient.dataAvailability === 'partial' && '🟡 Partial Dataset'}
                                    {selectedPatient.dataAvailability === 'no-data' && '🔴 No Synchronized Data'}
                                </span>
                                {selectedPatient.dataAvailability === 'complete' && (
                                    <div className="data-quality-details">
                                        <span className="data-detail">
                                            📈 {Number(selectedPatient.dailyECGSummaryCount || 0)} days of ECG data
                                        </span>
                                        <span className="data-detail">
                                            🩸 {Number(selectedPatient.glucoseReadingCount || 0)} glucose measurements
                                        </span>
                                        {selectedPatient.totalOriginalECGReadings && Number(selectedPatient.totalOriginalECGReadings) > 0 && (
                                            <span className="data-detail">
                                                ⚡ {Number(selectedPatient.totalOriginalECGReadings).toLocaleString()} original ECG samples
                                            </span>
                                        )}
                                        {selectedPatient.earliestReading && selectedPatient.latestReading && (
                                            <span className="data-detail">
                                                📅 {new Date(selectedPatient.earliestReading).toLocaleDateString()} - {new Date(selectedPatient.latestReading).toLocaleDateString()}
                                            </span>
                                        )}
                                    </div>
                                )}
                            </div>
                        )}
                    </div>

                    <div className="time-range-selector">
                        <label htmlFor="time-range-select">
                            Analysis Time Period:
                            <span className="label-description">Filter data by specific time ranges for focused analysis</span>
                        </label>
                        <select
                            id="time-range-select"
                            value={timeRange}
                            onChange={(e) => handleTimeRangeChange(e.target.value)}
                            disabled={loading || availableTimeRanges.length === 0}
                            title="Select a time period to focus your analysis. Different time ranges may reveal different patterns in the ECG-glucose correlation."
                            aria-describedby="time-range-help"
                        >
                            {availableTimeRanges.length === 0 ? (
                                <option value="all">⏳ Loading time ranges...</option>
                            ) : (
                                availableTimeRanges.map(range => (
                                    <option key={range.id} value={range.id}>
                                        📅 {range.label}
                                    </option>
                                ))
                            )}
                        </select>
                        <div id="time-range-help" className="help-text">
                            {!selectedPatient && (
                                <span className="help-message info">
                                    Select a patient first to see available time ranges
                                </span>
                            )}
                            {selectedPatient && availableTimeRanges.length === 0 && !loading && (
                                <span className="help-message warning">
                                    No time range data available for this patient
                                </span>
                            )}
                            {selectedPatient && availableTimeRanges.length > 0 && (
                                <span className="help-message success">
                                    {availableTimeRanges.length} time period{availableTimeRanges.length > 1 ? 's' : ''} available for analysis
                                </span>
                            )}
                        </div>
                    </div>

                    {/* Analysis view selection */}
                    <div className="view-controls">
                        <div className="view-tabs-container">
                            <div className="view-tabs-label">
                                <span>Analysis Views:</span>
                                <span className="view-tabs-description">Choose your analysis focus</span>
                            </div>
                            <div className="view-tabs" role="tablist" aria-label="Analysis view selection">
                                <button
                                    className={`tab-button ${activeView === 'correlation' ? 'active' : ''}`}
                                    onClick={() => setActiveView('correlation')}
                                    role="tab"
                                    aria-selected={activeView === 'correlation'}
                                    aria-controls="correlation-panel"
                                    title="View ECG-Glucose Correlation Analysis - Explore the relationship between heart rhythm patterns and blood glucose levels with interactive charts and statistical analysis"
                                >
                                    <span className="tab-icon">📈</span>
                                    <span className="tab-text">ECG-Glucose Correlation</span>
                                    <span className="tab-description">Heart rhythm & glucose patterns</span>
                                </button>
                                <button
                                    className={`tab-button ${activeView === 'recommendations' ? 'active' : ''}`}
                                    onClick={() => setActiveView('recommendations')}
                                    role="tab"
                                    aria-selected={activeView === 'recommendations'}
                                    aria-controls="recommendations-panel"
                                    title="AI-Powered Health Recommendations - Get personalized insights and recommendations based on the patient's ECG and glucose data patterns"
                                >
                                    <span className="tab-icon">💡</span>
                                    <span className="tab-text">AI Health Insights</span>
                                    <span className="tab-description">Personalized recommendations</span>
                                </button>
                                <button
                                    className={`tab-button ${activeView === 'promptTesting' ? 'active' : ''}`}
                                    onClick={() => setActiveView('promptTesting')}
                                    role="tab"
                                    aria-selected={activeView === 'promptTesting'}
                                    aria-controls="prompt-testing-panel"
                                    title="AI Prompt Testing Laboratory - Experiment with different AI prompts and compare responses for research and development purposes"
                                >
                                    <span className="tab-icon">🧪</span>
                                    <span className="tab-text">AI Prompt Lab</span>
                                    <span className="tab-description">Test & compare AI responses</span>
                                </button>
                                <button
                                    className={`tab-button ${activeView === 'analytics' ? 'active' : ''}`}
                                    onClick={() => setActiveView('analytics')}
                                    role="tab"
                                    aria-selected={activeView === 'analytics'}
                                    aria-controls="analytics-panel"
                                    title="Advanced Analytics Dashboard - Deep dive into statistical analysis, trends, and comprehensive data visualization"
                                >
                                    <span className="tab-icon">📊</span>
                                    <span className="tab-text">Advanced Analytics</span>
                                    <span className="tab-description">Statistical analysis & trends</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {error && (
                <div className="error-banner">
                    <div className="error-icon">⚠️</div>
                    <div className="error-content">
                        <h4>Error Loading Data</h4>
                        <p>{error}</p>
                        <div className="error-actions">
                            <button onClick={loadD1NAMOPatients} className="retry-button" disabled={loading}>
                                {loading ? '⏳ Retrying...' : '🔄 Retry'}
                            </button>
                            <button onClick={() => setError(null)} className="dismiss-button">
                                ✖️ Dismiss
                            </button>
                        </div>
                    </div>
                </div>
            )}

            {selectedPatient && (
                <div className="patient-summary">
                    <div className="patient-info">
                        <h3>{selectedPatient.name}</h3>
                        <div className="patient-details">
                            <span>Age: {selectedPatient.age}</span>
                            <span>Gender: {selectedPatient.gender}</span>
                            <span>Condition: {selectedPatient.condition}</span>
                            {selectedPatient.studyPhase && (
                                <span>Study Phase: {selectedPatient.studyPhase}</span>
                            )}
                            {selectedPatient.baselineHbA1c && (
                                <span>Baseline HbA1c: {selectedPatient.baselineHbA1c}%</span>
                            )}
                        </div>
                        {patientDataQuality && (
                            <div className="data-quality-summary">
                                <h4>📊 Data Quality Assessment</h4>
                                <div className="quality-metrics">
                                    <span className="metric">ECG Readings: <strong>{patientDataQuality.ecgCount || 0}</strong></span>
                                    <span className="metric">Glucose Readings: <strong>{patientDataQuality.glucoseCount || 0}</strong></span>
                                    <span className="metric">ECG Features: <strong>{patientDataQuality.featuresCount || 0}</strong></span>
                                    <span className={`quality-badge ${patientDataQuality.dataQuality}`}>
                                        {patientDataQuality.dataQuality === 'excellent' && '✅ Excellent'}
                                        {patientDataQuality.dataQuality === 'good' && '👍 Good'}
                                        {patientDataQuality.dataQuality === 'limited' && '⚠️ Limited'}
                                    </span>
                                </div>
                                {patientDataQuality.earliestECG && (
                                    <div className="time-range-info">
                                        <small>ECG Range: {new Date(patientDataQuality.earliestECG).toLocaleDateString()} - {new Date(patientDataQuality.latestECG).toLocaleDateString()}</small>
                                        {patientDataQuality.earliestGlucose && (
                                            <small>Glucose Range: {new Date(patientDataQuality.earliestGlucose).toLocaleDateString()} - {new Date(patientDataQuality.latestGlucose).toLocaleDateString()}</small>
                                        )}
                                    </div>
                                )}
                            </div>
                        )}
                    </div>

                    <div className="dashboard-stats">
                        <div className="stat-item">
                            <span className="stat-value">{dashboardStats.totalReadings || 0}</span>
                            <span className="stat-label">Daily Summaries</span>
                        </div>
                        <div className="stat-item">
                            <span className="stat-value">{dashboardStats.totalOriginalReadings || 'N/A'}</span>
                            <span className="stat-label">Original ECG Readings</span>
                        </div>
                        <div className="stat-item">
                            <span className="stat-value">{dashboardStats.compressionRatio || 'N/A'}</span>
                            <span className="stat-label">Compression Ratio</span>
                        </div>
                        <div className="stat-item">
                            <span className="stat-value">{dashboardStats.avgGlucose || 'N/A'}</span>
                            <span className="stat-label">Avg Glucose (mg/dL)</span>
                        </div>
                        <div className="stat-item">
                            <span className="stat-value">{dashboardStats.avgHeartRate || 'N/A'}</span>
                            <span className="stat-label">Avg Heart Rate (BPM)</span>
                        </div>
                        <div className="stat-item">
                            <span className="stat-value">{dashboardStats.avgHRV || 'N/A'}</span>
                            <span className="stat-label">ECG Variability</span>
                        </div>
                        <div className="stat-item">
                            <span className="stat-value">{dashboardStats.timeInRange || 'N/A'}%</span>
                            <span className="stat-label">Time in Range (70-180)</span>
                        </div>
                        <div className="stat-item">
                            <span className={`stat-value quality-${dashboardStats.dataQuality?.toLowerCase()}`}>{dashboardStats.dataQuality || 'N/A'}</span>
                            <span className="stat-label">Daily Sync Quality</span>
                        </div>
                        <div className="stat-item">
                            <span className="stat-value">{dashboardStats.goodSyncPercent || 'N/A'}%</span>
                            <span className="stat-label">Good Daily Sync</span>
                        </div>
                        <div className="stat-item">
                            <span className="stat-value">{dashboardStats.avgSyncTime || 'N/A'} hrs</span>
                            <span className="stat-label">Avg Sync Time</span>
                        </div>
                    </div>
                </div>
            )}

            {loading && (
                <div className="loading-overlay">
                    <div className="loading-container">
                        <div className="loading-spinner">
                            <div className="spinner-ring"></div>
                            <div className="spinner-ring"></div>
                            <div className="spinner-ring"></div>
                        </div>
                        <div className="loading-content">
                            <h3>🧬 Loading D1NAMO Data</h3>
                            <p>
                                {!selectedPatient ?
                                    'Connecting to Neo4j database and loading patient list...' :
                                    `Loading synchronized ECG and glucose data for ${selectedPatient.name}...`
                                }
                            </p>
                            <div className="loading-progress">
                                <div className="progress-bar">
                                    <div className="progress-fill"></div>
                                </div>
                                <span className="progress-text">Please wait while we fetch your data</span>
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {selectedPatient && (
                <div className="dashboard-content">
                    {activeView === 'correlation' && (
                        <>
                            <div className="analysis-section">
                                <h2>ECG-Glucose Correlation Analysis</h2>
                                <ECGGlucoseCorrelation
                                    synchronizedData={synchronizedData}
                                    patientInfo={selectedPatient}
                                    timeRange={timeRange * 24} // convert to hours
                                    onDataPointClick={handleDataPointClick}
                                />
                            </div>

                            {ecgLeadData.length > 0 && (
                                <div className="waveform-section">
                                    <h2>ECG Waveform Analysis</h2>
                                    <div className="waveform-info">
                                        <p>
                                            <strong>Selected Timestamp:</strong> {selectedTimestamp ? new Date(selectedTimestamp).toLocaleString() : 'None'}
                                        </p>
                                        <p>
                                            <strong>Signal Quality:</strong> {ecgLeadData[0]?.quality || 'Unknown'}
                                        </p>
                                    </div>
                                    <ECGWaveformViewer
                                        ecgData={ecgLeadData}
                                        selectedLead={selectedLead}
                                        onLeadChange={handleLeadChange}
                                        showGrid={true}
                                        autoScale={true}
                                        samplingRate={ecgLeadData[0]?.samplingRate || 1000}
                                    />
                                </div>
                            )}
                        </>
                    )}

                    {activeView === 'recommendations' && (
                        <div className="analysis-section">
                            <h2>Personalized Health Recommendations</h2>
                            <PatientHealthRecommendations
                                patientData={patientData}
                                preferredTechnique="structured"
                                showTechniqueSelector={true}
                                onRecommendationsUpdate={() => { /* no-op */ }}
                            />
                        </div>
                    )}

                    {activeView === 'promptTesting' && (
                        <div className="analysis-section">
                            <h2>🧪 Prompt Testing Laboratory</h2>
                            <PromptTesting />
                        </div>
                    )}

                    {activeView === 'analytics' && (
                        <div className="analysis-section">
                            <h2>📊 Patient Analytics</h2>
                            <div className="analytics-placeholder">
                                <p>📈 Advanced analytics features coming soon...</p>
                                <p>This section will include:</p>
                                <ul>
                                    <li>Patient outcome trends</li>
                                    <li>Treatment effectiveness analysis</li>
                                    <li>Population health insights</li>
                                    <li>Comparative analysis tools</li>
                                </ul>
                            </div>
                        </div>
                    )}
                </div>
            )}

            {selectedPatient && !loading && synchronizedData.length === 0 && (
                <div className="no-data-message">
                    <div className="no-data-icon">📊</div>
                    <h3>No Synchronized Data Available</h3>
                    <p>
                        No ECG-glucose synchronized data found for {selectedPatient.name}
                        in the selected time range ({availableTimeRanges.find(r => r.id === timeRange)?.label || timeRange}).
                    </p>
                    {selectedPatient.dataAvailability === 'no-data' ? (
                        <div className="no-data-guidance">
                            <p><strong>⚠️ Patient {selectedPatient.patientId} has no physiological monitoring data.</strong></p>
                            <p>Based on the D1NAMO database analysis:</p>
                            <ul>
                                <li>Patients 001, 002, and 003 have complete ECG-glucose data</li>
                                <li>Patients 004-009 are currently missing synchronized readings</li>
                                <li>Consider selecting patients 001-003 for full dashboard functionality</li>
                            </ul>
                        </div>
                    ) : (
                        <div className="data-guidance">
                            <p>This could mean:</p>
                            <ul>
                                <li>The selected time range doesn't contain synchronized data</li>
                                <li>ECG and glucose readings are more than 15 minutes apart</li>
                                <li>Data import is incomplete for this patient</li>
                                <li>Data hasn't been imported yet</li>
                                <li>There are no synchronized measurements for this patient</li>
                            </ul>
                            <p><strong>Try:</strong> Selecting a different time range or patient with complete data (001-003).</p>
                        </div>
                    )}
                    <button onClick={loadAllSynchronizedData} className="refresh-button">
                        Refresh Data
                    </button>
                </div>
            )}

            <div className="dashboard-footer">
                <div className="footer-info">
                    <p>
                        <strong>D1NAMO Dataset:</strong> Synchronized ECG and continuous glucose monitoring
                        data for diabetes research and clinical analysis.
                    </p>
                    {dataSummary && (
                        <div className="dataset-summary">
                            <p>
                                <strong>Dataset Overview:</strong> {dataSummary.totalPatients} patients,
                                {dataSummary.totalECG} daily ECG summaries, {dataSummary.totalGlucose} glucose measurements
                            </p>
                            {dataSummary.totalOriginalECGReadings && (
                                <p>
                                    <strong>Data Compression:</strong> {dataSummary.totalOriginalECGReadings.toLocaleString()} original ECG readings
                                    compressed to {dataSummary.totalECG} daily summaries
                                    (ratio: {dataSummary.compressionRatio || 'N/A'})
                                </p>
                            )}
                            <p>
                                <strong>Monitoring Period:</strong> {dataSummary.monitoringDays} days
                                ({new Date(dataSummary.earliestData).toLocaleDateString()} - {new Date(dataSummary.latestData).toLocaleDateString()})
                            </p>
                            <p>
                                <strong>Conditions:</strong> {dataSummary.conditions?.join(', ')}
                            </p>
                        </div>
                    )}
                    <p>
                        <strong>Data Structure:</strong> ECG data aggregated into daily summaries with statistical measures
                        (mean, min, max, std dev) and estimated heart rate. Synchronized with glucose readings by date.
                    </p>
                </div>

                <div className="footer-actions">
                    <div className="action-group primary-actions">
                        <button
                            onClick={loadD1NAMOPatients}
                            disabled={loading}
                            className="refresh-btn primary-action"
                            title="Reload patient list from Neo4j database. Use this if new patients have been added or if you're experiencing connection issues."
                        >
                            <span className="btn-icon">🔄</span>
                            <span className="btn-text">Refresh Patient List</span>
                            <span className="btn-description">Reload from database</span>
                        </button>
                        {selectedPatient && (
                            <button
                                onClick={loadAllSynchronizedData}
                                disabled={loading}
                                className="refresh-btn secondary-action"
                                title={`Reload synchronized ECG and glucose data for ${selectedPatient.name}. This will fetch the latest data and refresh all visualizations.`}
                            >
                                <span className="btn-icon">🔄</span>
                                <span className="btn-text">Refresh Patient Data</span>
                                <span className="btn-description">Update charts & analysis</span>
                            </button>
                        )}
                    </div>

                    {selectedPatient && (
                        <div className="action-group data-actions">
                            {synchronizedData.length > 0 && (
                                <button
                                    onClick={handleExportData}
                                    disabled={loading}
                                    className="export-btn"
                                    title={`Export all loaded data for ${selectedPatient.name} as a JSON file. Includes synchronized ECG-glucose data, analysis results, and metadata.`}
                                >
                                    <span className="btn-icon">📤</span>
                                    <span className="btn-text">Export Analysis Data</span>
                                    <span className="btn-description">Download JSON file</span>
                                </button>
                            )}
                            <button
                                onClick={handleClearData}
                                disabled={loading}
                                className="clear-btn"
                                title="Clear all loaded data from memory. This will not affect the database, only the current session. Use this to free up memory or start fresh."
                            >
                                <span className="btn-icon">🗑️</span>
                                <span className="btn-text">Clear Session Data</span>
                                <span className="btn-description">Reset current analysis</span>
                            </button>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default D1NAMODashboard;
