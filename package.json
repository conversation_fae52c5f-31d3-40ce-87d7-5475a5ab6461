{"name": "healthhub-research-platform", "private": true, "version": "0.1.0", "type": "module", "description": "HealthHub Research Platform - Healthcare Analytics with Neo4j and AI Integration", "scripts": {"dev": "vite", "dev:http": "vite", "dev:web": "vite", "dev:api": "npx wrangler pages dev --port=8787 --local", "dev:local": "concurrently \"npm:dev:api\" \"npm:dev:web\"", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:neo4j": "vitest run src/features/neo4j/tests/", "populate-db": "node scripts/database/populateDatabase.js", "test-db": "node -e \"require('./scripts/populateDatabase.js').testConnection().then(() => process.exit(0)).catch(() => process.exit(1))\"", "deploy": "npm run build && git add . && git commit -m \"deploy: automated build and deployment\" || echo \"No changes to commit\" && git push && wrangler pages deploy dist --project-name=healthhub-research-platform", "deploy-full": "npm run build && git add . && git commit -m \"deploy: automated build and deployment\" && git push && wrangler pages deploy dist --project-name=healthhub-research-platform && curl -s https://healthhub-research-platform.pages.dev/api/health", "setup-secrets": "powershell -ExecutionPolicy Bypass -File setup-secrets.ps1", "wrangler:dev": "wrangler pages dev dist --port=8787", "wrangler:tail": "wrangler pages deployment tail --project-name=healthhub-research-platform", "setup": "node scripts/development/quick-start.js", "setup:env": "node scripts/development/setup-local-dev.js", "validate:env": "node scripts/development/validate-env.js", "test:local": "node scripts/development/test-local-dev.js", "health:check": "curl -s http://127.0.0.1:8787/api/health | jq ."}, "dependencies": {"@react-three/drei": "^10.7.6", "@react-three/fiber": "^9.3.0", "chart.js": "^4.5.0", "chartjs-adapter-date-fns": "^3.0.0", "chartjs-plugin-annotation": "^3.1.0", "csv-parser": "^3.2.0", "dotenv": "^17.2.2", "neo4j-driver": "^5.28.1", "react": "^19.1.1", "react-chartjs-2": "^5.3.0", "react-dom": "^19.1.1", "three": "^0.180.0"}, "devDependencies": {"@eslint/js": "^9.33.0", "@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/react": "^19.1.13", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^5.0.0", "@vitest/coverage-v8": "^3.2.4", "concurrently": "^9.1.2", "eslint": "^9.33.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "jsdom": "^27.0.0", "node-fetch": "^3.3.2", "vite": "^7.1.2", "vitest": "^3.2.4"}}