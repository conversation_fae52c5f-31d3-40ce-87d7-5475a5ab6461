.error-boundary {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 400px;
    padding: 2rem;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    border: 1px solid #dee2e6;
    margin: 1rem;
}

.error-boundary-content {
    max-width: 600px;
    text-align: center;
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.error-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.error-boundary h2 {
    color: #dc3545;
    margin-bottom: 1rem;
    font-size: 1.5rem;
}

.error-boundary p {
    color: #6c757d;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.error-details {
    text-align: left;
    margin: 1.5rem 0;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    overflow: hidden;
}

.error-details summary {
    background: #f8f9fa;
    padding: 0.75rem 1rem;
    cursor: pointer;
    font-weight: 500;
    border-bottom: 1px solid #dee2e6;
}

.error-details summary:hover {
    background: #e9ecef;
}

.error-message,
.error-stack {
    padding: 1rem;
    border-bottom: 1px solid #dee2e6;
}

.error-message:last-child,
.error-stack:last-child {
    border-bottom: none;
}

.error-stack pre {
    background: #f8f9fa;
    padding: 0.75rem;
    border-radius: 4px;
    overflow-x: auto;
    font-size: 0.875rem;
    margin-top: 0.5rem;
    white-space: pre-wrap;
    word-break: break-word;
}

.error-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin: 1.5rem 0;
}

.retry-button,
.reload-button {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.retry-button {
    background: #007bff;
    color: white;
}

.retry-button:hover {
    background: #0056b3;
    transform: translateY(-1px);
}

.reload-button {
    background: #6c757d;
    color: white;
}

.reload-button:hover {
    background: #545b62;
    transform: translateY(-1px);
}

.error-help {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
    margin-top: 1.5rem;
}

.error-help p {
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.error-help ul {
    text-align: left;
    margin: 0.5rem 0 0 1rem;
}

.error-help li {
    margin-bottom: 0.25rem;
    color: #6c757d;
}
