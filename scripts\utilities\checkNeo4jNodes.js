/**
 * Check Neo4j nodes for D1NAMO dashboard
 * Uses direct connection to Neo4j AuraDB
 */

import dotenv from 'dotenv';
import neo4j from 'neo4j-driver';

// Load environment variables
dotenv.config();

const NEO4J_URI = process.env.VITE_NEO4J_URI;
const NEO4J_USERNAME = process.env.VITE_NEO4J_USERNAME;
const NEO4J_PASSWORD = process.env.VITE_NEO4J_PASSWORD;
const NEO4J_DATABASE = process.env.VITE_NEO4J_DATABASE || 'neo4j';

async function checkNeo4jNodes() {
  let driver = null;

  try {
    console.log('🔗 Connecting to Neo4j AuraDB...');
    console.log(`URI: ${NEO4J_URI.replace(/\/\/.*@/, '//***@')}`);

    driver = neo4j.driver(
      NEO4J_URI,
      neo4j.auth.basic(NEO4J_USERNAME, NEO4J_PASSWORD),
      {
        disableLosslessIntegers: true,
        maxConnectionLifetime: 3 * 60 * 60 * 1000, // 3 hours
        maxConnectionPoolSize: 50,
        connectionAcquisitionTimeout: 2 * 60 * 1000, // 2 minutes
      }
    );

    const session = driver.session({ database: NEO4J_DATABASE });

    try {
      // Test connection
      console.log('🔍 Testing connection...');
      const testResult = await session.run('RETURN 1 as test, datetime() as timestamp');
      console.log('✅ Connected successfully');

      // Get all node labels
      console.log('\n=== Available Node Labels ===');
      const labelsResult = await session.run('CALL db.labels() YIELD label RETURN label ORDER BY label');
      const labels = labelsResult.records.map(record => record.get('label'));
      labels.forEach(label => console.log(`- ${label}`));

      // Get node counts for each label
      console.log('\n=== Node Counts ===');
      for (const label of labels) {
        try {
          const countResult = await session.run(`MATCH (n:\`${label}\`) RETURN count(n) as count`);
          const countValue = countResult.records[0].get('count');
          const count = neo4j.isInt(countValue) ? countValue.toNumber() : parseInt(countValue);
          console.log(`${label}: ${count} nodes`);
        } catch (error) {
          console.log(`${label}: Error getting count - ${error.message}`);
        }
      }

      // Check specifically for D1NAMO related nodes
      console.log('\n=== D1NAMO Specific Nodes ===');

      // Check for D1NAMOSubject patients
      try {
        const d1namoResult = await session.run(`
                    MATCH (p:Patient:D1NAMOSubject)
                    RETURN p.patientId as patientId, p.name as name, p.condition as condition
                    ORDER BY p.patientId
                    LIMIT 10
                `);

        if (d1namoResult.records.length > 0) {
          console.log('D1NAMO Patients:');
          d1namoResult.records.forEach(record => {
            console.log(`  - ${record.get('patientId')}: ${record.get('name')} (${record.get('condition')})`);
          });
        } else {
          console.log('No D1NAMO patients found');
        }
      } catch (error) {
        console.log(`Error querying D1NAMO patients: ${error.message}`);
      }

      // Check for ECG data
      try {
        const ecgResult = await session.run(`
                    MATCH (e:ECGReading)
                    RETURN count(e) as ecgCount, min(e.timestamp) as earliest, max(e.timestamp) as latest
                `);

        if (ecgResult.records.length > 0) {
          const record = ecgResult.records[0];
          const countValue = record.get('ecgCount');
          const count = neo4j.isInt(countValue) ? countValue.toNumber() : parseInt(countValue);
          console.log(`ECG Readings: ${count}`);
          console.log(`  Date range: ${record.get('earliest')} to ${record.get('latest')}`);
        }
      } catch (error) {
        console.log(`Error querying ECG data: ${error.message}`);
      }

      // Check for Glucose data
      try {
        const glucoseResult = await session.run(`
                    MATCH (g:GlucoseReading)
                    RETURN count(g) as glucoseCount, min(g.timestamp) as earliest, max(g.timestamp) as latest
                `);

        if (glucoseResult.records.length > 0) {
          const record = glucoseResult.records[0];
          const countValue = record.get('glucoseCount');
          const count = neo4j.isInt(countValue) ? countValue.toNumber() : parseInt(countValue);
          console.log(`Glucose Readings: ${count}`);
          console.log(`  Date range: ${record.get('earliest')} to ${record.get('latest')}`);
        }
      } catch (error) {
        console.log(`Error querying Glucose data: ${error.message}`);
      }

      // Check relationships
      console.log('\n=== Relationship Types ===');
      try {
        const relsResult = await session.run('CALL db.relationshipTypes() YIELD relationshipType RETURN relationshipType ORDER BY relationshipType');
        const relationships = relsResult.records.map(record => record.get('relationshipType'));
        relationships.forEach(rel => console.log(`- ${rel}`));
      } catch (error) {
        console.log(`Error querying relationships: ${error.message}`);
      }

      // Sample synchronized data query
      console.log('\n=== Sample D1NAMO Dashboard Query ===');
      try {
        const sampleResult = await session.run(`
                    MATCH (p:D1NAMOSubject)
                    OPTIONAL MATCH (p)-[:HAS_DAILY_ECG_SUMMARY]->(d:DailyECGSummary)
                    OPTIONAL MATCH (p)-[:HAD_READING]->(g:GlucoseReading)
                    RETURN p.patientId as patientId,
                           count(DISTINCT d) as dailyECGSummaries,
                           sum(d.readingCount) as originalECGReadings,
                           count(DISTINCT g) as glucoseReadings
                    ORDER BY p.patientId
                    LIMIT 5
                `);

        if (sampleResult.records.length > 0) {
          console.log('Sample patient data:');
          sampleResult.records.forEach(record => {
            const dailySummariesValue = record.get('dailyECGSummaries');
            const originalReadingsValue = record.get('originalECGReadings');
            const glucoseValue = record.get('glucoseReadings');

            const dailySummariesCount = neo4j.isInt(dailySummariesValue) ? dailySummariesValue.toNumber() : parseInt(dailySummariesValue);
            const originalReadingsCount = neo4j.isInt(originalReadingsValue) ? originalReadingsValue.toNumber() : parseInt(originalReadingsValue);
            const glucoseCount = neo4j.isInt(glucoseValue) ? glucoseValue.toNumber() : parseInt(glucoseValue);

            console.log(`  Patient ${record.get('patientId')}: Daily Summaries=${dailySummariesCount}, Original ECG=${originalReadingsCount || 0}, Glucose=${glucoseCount}`);
          });
        } else {
          console.log('No sample data found');
        }
      } catch (error) {
        console.log(`Error with sample query: ${error.message}`);
      }

    } finally {
      await session.close();
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
    if (error.code) {
      console.error('Error code:', error.code);
    }
  } finally {
    if (driver) {
      await driver.close();
    }
  }
}

// Run the check
checkNeo4jNodes().then(() => {
  console.log('\n✅ Neo4j node check completed');
  process.exit(0);
}).catch((error) => {
  console.error('❌ Script failed:', error.message);
  process.exit(1);
});
