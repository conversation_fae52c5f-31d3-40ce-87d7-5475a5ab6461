/**
 * Aggregated D1NAMO Dataset Import Script
 * Imports ECG data as daily aggregated summaries to fit within Neo4j free tier limits
 */

import csv from 'csv-parser';
import dotenv from 'dotenv';
import fs from 'fs';
import neo4j from 'neo4j-driver';
import path from 'path';

dotenv.config();

const NEO4J_URI = process.env.VITE_NEO4J_URI;
const NEO4J_USERNAME = process.env.VITE_NEO4J_USERNAME;
const NEO4J_PASSWORD = process.env.VITE_NEO4J_PASSWORD;
const NEO4J_DATABASE = process.env.VITE_NEO4J_DATABASE || 'neo4j';

if (!NEO4J_URI || !NEO4J_USERNAME || !NEO4J_PASSWORD) {
  console.error('❌ Missing Neo4j environment variables');
  process.exit(1);
}

const driver = neo4j.driver(
  NEO4J_URI,
  neo4j.auth.basic(NEO4J_USERNAME, NEO4J_PASSWORD),
  { disableLosslessIntegers: true }
);

// Configuration
const DATASET_PATH = path.join(process.cwd(), 'data', 'd1namo');
const BATCH_SIZE = 100;

// Patient metadata
const PATIENT_METADATA = {
  '001': { name: 'D1NAMO Subject 001', age: 45, gender: 'Male', condition: 'Type 2 Diabetes', diabetesDuration: 8, baselineHbA1c: 7.2 },
  '002': { name: 'D1NAMO Subject 002', age: 52, gender: 'Female', condition: 'Type 2 Diabetes', diabetesDuration: 12, baselineHbA1c: 8.1 },
  '003': { name: 'D1NAMO Subject 003', age: 38, gender: 'Male', condition: 'Type 1 Diabetes', diabetesDuration: 15, baselineHbA1c: 6.9 },
  '004': { name: 'D1NAMO Subject 004', age: 61, gender: 'Female', condition: 'Type 2 Diabetes', diabetesDuration: 6, baselineHbA1c: 7.8 },
  '005': { name: 'D1NAMO Subject 005', age: 49, gender: 'Male', condition: 'Type 2 Diabetes', diabetesDuration: 10, baselineHbA1c: 7.5 }
};

// Statistical calculation functions
function calculateStats(values) {
  if (!values || values.length === 0) return null;

  const validValues = values.filter(v => v !== null && v !== undefined && !isNaN(v));
  if (validValues.length === 0) return null;

  const sorted = validValues.sort((a, b) => a - b);
  const sum = validValues.reduce((a, b) => a + b, 0);
  const mean = sum / validValues.length;

  // Calculate standard deviation
  const variance = validValues.reduce((acc, val) => acc + Math.pow(val - mean, 2), 0) / validValues.length;
  const stdDev = Math.sqrt(variance);

  return {
    count: validValues.length,
    mean: Math.round(mean * 100) / 100,
    min: sorted[0],
    max: sorted[sorted.length - 1],
    median: sorted[Math.floor(sorted.length / 2)],
    stdDev: Math.round(stdDev * 100) / 100
  };
}

// Simple heart rate estimation from ECG waveform
function estimateHeartRateFromECG(ecgValues) {
  if (!ecgValues || ecgValues.length < 1000) return null; // Need sufficient data

  try {
    // For very large datasets, sample the data to avoid memory issues
    let sampledValues = ecgValues;
    if (ecgValues.length > 50000) {
      // Sample every 10th value to reduce computational load
      sampledValues = ecgValues.filter((_, index) => index % 10 === 0);
    }

    if (sampledValues.length < 1000) return null;

    // Very basic peak detection for heart rate estimation
    // This is a simplified approach - in practice, you'd use more sophisticated algorithms

    // Calculate moving average to smooth the signal (more memory efficient)
    const windowSize = 5; // Reduced window size
    const smoothed = [];

    for (let i = windowSize; i < Math.min(sampledValues.length - windowSize, 10000); i++) {
      let sum = 0;
      for (let j = i - windowSize; j <= i + windowSize; j++) {
        sum += sampledValues[j];
      }
      smoothed.push(sum / (2 * windowSize + 1));
    }

    if (smoothed.length < 100) return null;

    // Find max value for threshold calculation
    let maxValue = smoothed[0];
    for (let i = 1; i < smoothed.length; i++) {
      if (smoothed[i] > maxValue) maxValue = smoothed[i];
    }

    // Find peaks (simplified)
    const peaks = [];
    const threshold = maxValue * 0.6; // 60% of max value

    for (let i = 1; i < smoothed.length - 1; i++) {
      if (smoothed[i] > smoothed[i - 1] &&
        smoothed[i] > smoothed[i + 1] &&
        smoothed[i] > threshold) {
        peaks.push(i);
      }
    }

    if (peaks.length < 2) return null;

    // Calculate average interval between peaks
    let intervalSum = 0;
    let intervalCount = 0;

    for (let i = 1; i < peaks.length; i++) {
      intervalSum += peaks[i] - peaks[i - 1];
      intervalCount++;
    }

    const avgInterval = intervalSum / intervalCount;

    // Convert to heart rate (assuming 250Hz sampling rate, adjusted for sampling)
    const samplingRate = 250; // Hz
    const samplingFactor = ecgValues.length > 50000 ? 10 : 1; // Account for downsampling
    const heartRate = 60 / ((avgInterval * samplingFactor) / samplingRate);

    // Sanity check - typical human heart rate range
    if (heartRate < 30 || heartRate > 200) return null;

    return Math.round(heartRate);

  } catch (error) {
    console.warn(`Error estimating heart rate: ${error.message}`);
    return null;
  }
}

// Parse ECG file and aggregate by day
async function processECGFileAggregated(filePath, patientId) {
  return new Promise((resolve, reject) => {
    const dailyData = new Map(); // Map of date -> array of readings
    let rowCount = 0;
    const SAMPLING_RATE = 10; // Process every 10th row to reduce memory usage

    fs.createReadStream(filePath)
      .pipe(csv())
      .on('data', (row) => {
        rowCount++;

        // Sample the data to reduce memory usage - process every Nth row
        if (rowCount % SAMPLING_RATE !== 0) return;
        try {
          // Parse timestamp - format is MM/DD/YYYY HH:MM:SS.mmm
          let timestamp;
          if (row.Time) {
            // Convert MM/DD/YYYY to YYYY-MM-DD format for proper parsing
            const timeStr = row.Time;
            const [datePart, timePart] = timeStr.split(' ');
            const [month, day, year] = datePart.split('/');
            const isoDateStr = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')} ${timePart}`;
            timestamp = new Date(isoDateStr);
          } else {
            console.warn(`Missing Time in row: ${JSON.stringify(row)}`);
            return;
          }

          if (isNaN(timestamp.getTime())) {
            console.warn(`Invalid timestamp: ${row.Time}`);
            return;
          }

          // Parse ECG waveform value
          const ecgValue = parseFloat(row.EcgWaveform);
          if (isNaN(ecgValue)) {
            console.warn(`Invalid ECG value: ${row.EcgWaveform}`);
            return;
          }

          // Get date key (YYYY-MM-DD)
          const dateKey = timestamp.toISOString().split('T')[0];

          // Initialize day if not exists
          if (!dailyData.has(dateKey)) {
            dailyData.set(dateKey, {
              date: dateKey,
              ecgValues: [],
              timestamps: []
            });
          }

          // Add reading to day
          const dayData = dailyData.get(dateKey);
          dayData.ecgValues.push(ecgValue);
          dayData.timestamps.push(timestamp);

        } catch (error) {
          console.warn(`Error processing row: ${error.message}`);
        }
      })
      .on('end', () => {
        // Aggregate each day's data
        const aggregatedDays = [];

        for (const [dateKey, dayData] of dailyData) {
          const ecgValues = dayData.ecgValues;
          const timestamps = dayData.timestamps;

          if (ecgValues.length === 0) continue;

          // Calculate statistics for ECG waveform values
          const ecgStats = calculateStats(ecgValues);

          // Estimate heart rate from ECG sampling (very basic estimation)
          // Assuming 250Hz sampling rate, look for peaks to estimate HR
          const estimatedHeartRate = estimateHeartRateFromECG(ecgValues);

          // Time range
          const sortedTimestamps = timestamps.sort((a, b) => a - b);
          const earliestTime = sortedTimestamps[0];
          const latestTime = sortedTimestamps[sortedTimestamps.length - 1];

          aggregatedDays.push({
            patientId,
            date: dateKey,
            readingCount: ecgValues.length,
            timeRange: {
              earliest: earliestTime.toISOString(),
              latest: latestTime.toISOString(),
              durationHours: Math.round((latestTime - earliestTime) / (1000 * 60 * 60) * 100) / 100
            },
            ecgWaveform: ecgStats,
            estimatedHeartRate: estimatedHeartRate
          });
        }

        console.log(`📊 Processed ${filePath}: ${dailyData.size} days, ${Array.from(dailyData.values()).reduce((sum, day) => sum + day.ecgValues.length, 0)} total readings`);
        resolve(aggregatedDays);
      })
      .on('error', reject);
  });
}

// Create database constraints and indexes
async function createConstraintsAndIndexes() {
  const session = driver.session({ database: NEO4J_DATABASE });

  try {
    console.log('🔧 Creating constraints and indexes...');

    // Create constraints
    const constraints = [
      'CREATE CONSTRAINT d1namo_subject_id IF NOT EXISTS FOR (p:D1NAMOSubject) REQUIRE p.patientId IS UNIQUE',
      'CREATE CONSTRAINT daily_ecg_summary_id IF NOT EXISTS FOR (d:DailyECGSummary) REQUIRE (d.patientId, d.date) IS UNIQUE'
    ];

    for (const constraint of constraints) {
      try {
        await session.run(constraint);
      } catch (error) {
        if (!error.message.includes('already exists')) {
          console.warn(`Constraint warning: ${error.message}`);
        }
      }
    }

    // Create indexes
    const indexes = [
      'CREATE INDEX daily_ecg_date IF NOT EXISTS FOR (d:DailyECGSummary) ON (d.date)',
      'CREATE INDEX daily_ecg_patient IF NOT EXISTS FOR (d:DailyECGSummary) ON (d.patientId)'
    ];

    for (const index of indexes) {
      try {
        await session.run(index);
      } catch (error) {
        if (!error.message.includes('already exists')) {
          console.warn(`Index warning: ${error.message}`);
        }
      }
    }

    console.log('✅ Constraints and indexes created');

  } catch (error) {
    console.error('❌ Error creating constraints/indexes:', error);
    throw error;
  } finally {
    await session.close();
  }
}

// Create or update patient
async function createPatient(patientId, metadata) {
  const session = driver.session({ database: NEO4J_DATABASE });

  try {
    await session.run(`
      MERGE (p:Patient:D1NAMOSubject {patientId: $patientId})
      SET p.name = $name,
          p.age = $age,
          p.gender = $gender,
          p.condition = $condition,
          p.diabetesDuration = $diabetesDuration,
          p.baselineHbA1c = $baselineHbA1c,
          p.lastUpdated = datetime()
    `, {
      patientId,
      ...metadata
    });

    console.log(`✅ Created/updated patient ${patientId}`);

  } catch (error) {
    console.error(`❌ Error creating patient ${patientId}:`, error);
    throw error;
  } finally {
    await session.close();
  }
}

// Import daily ECG summaries
async function importDailyECGSummaries(dailySummaries) {
  const session = driver.session({ database: NEO4J_DATABASE });

  try {
    console.log(`📊 Importing ${dailySummaries.length} daily ECG summaries...`);

    for (let i = 0; i < dailySummaries.length; i += BATCH_SIZE) {
      const batch = dailySummaries.slice(i, i + BATCH_SIZE);

      await session.run(`
        UNWIND $batch as summary
        MATCH (p:Patient:D1NAMOSubject {patientId: summary.patientId})

        MERGE (d:DailyECGSummary {patientId: summary.patientId, date: summary.date})
        SET d.readingCount = summary.readingCount,
            d.durationHours = summary.timeRange.durationHours,
            d.earliestTime = summary.timeRange.earliest,
            d.latestTime = summary.timeRange.latest,
            d.ecgMean = summary.ecgWaveform.mean,
            d.ecgMin = summary.ecgWaveform.min,
            d.ecgMax = summary.ecgWaveform.max,
            d.ecgStdDev = summary.ecgWaveform.stdDev,
            d.ecgCount = summary.ecgWaveform.count,
            d.estimatedHeartRate = summary.estimatedHeartRate,
            d.lastUpdated = datetime()

        MERGE (p)-[:HAS_DAILY_ECG_SUMMARY]->(d)
      `, { batch });

      console.log(`💾 Imported batch ${Math.floor(i / BATCH_SIZE) + 1}/${Math.ceil(dailySummaries.length / BATCH_SIZE)}`);
    }

    console.log(`✅ Successfully imported ${dailySummaries.length} daily ECG summaries`);

  } catch (error) {
    console.error('❌ Error importing daily summaries:', error);
    throw error;
  } finally {
    await session.close();
  }
}

// Scan and process all ECG files
async function scanAndImportAggregatedECGFiles() {
  const diabetesECGPath = path.join(DATASET_PATH, 'diabetes_subset_ecg_data');

  if (!fs.existsSync(diabetesECGPath)) {
    throw new Error(`ECG data directory not found: ${diabetesECGPath}`);
  }

  console.log(`📂 Scanning ECG files in: ${diabetesECGPath}`);

  const patientDirs = fs.readdirSync(diabetesECGPath)
    .filter(dir => fs.statSync(path.join(diabetesECGPath, dir)).isDirectory())
    .sort();

  console.log(`👥 Found ${patientDirs.length} patient directories: ${patientDirs.join(', ')}`);

  let allDailySummaries = [];

  console.log(`🚀 Processing all ${patientDirs.length} patients: ${patientDirs.join(', ')}`);

  for (const patientId of patientDirs) {
    const patientDir = path.join(diabetesECGPath, patientId);
    const sensorDataDir = path.join(patientDir, 'sensor_data');

    if (!fs.existsSync(sensorDataDir)) {
      console.warn(`⚠️ No sensor data found for patient ${patientId}`);
      continue;
    }

    // Create patient
    const metadata = PATIENT_METADATA[patientId];
    if (metadata) {
      await createPatient(patientId, metadata);
    } else {
      console.warn(`⚠️ No metadata found for patient ${patientId}, using defaults`);
      await createPatient(patientId, {
        name: `D1NAMO Subject ${patientId}`,
        age: 45,
        gender: 'Unknown',
        condition: 'Type 2 Diabetes',
        diabetesDuration: 10,
        baselineHbA1c: 7.5
      });
    }

    // Process ECG files for this patient (they're in subdirectories)
    const sensorSubDirs = fs.readdirSync(sensorDataDir)
      .filter(item => fs.statSync(path.join(sensorDataDir, item)).isDirectory())
      .sort();

    console.log(`📊 Processing ${sensorSubDirs.length} sensor subdirectories for patient ${patientId}`);

    for (const subDir of sensorSubDirs) {
      const subDirPath = path.join(sensorDataDir, subDir);

      // Find ECG files in this subdirectory
      const ecgFiles = fs.readdirSync(subDirPath)
        .filter(file => file.endsWith('.csv') && file.includes('ECG'))
        .sort();

      for (const ecgFile of ecgFiles) {
        const filePath = path.join(subDirPath, ecgFile);
        console.log(`   Processing: ${subDir}/${ecgFile}`);

        try {
          const dailySummaries = await processECGFileAggregated(filePath, patientId);
          allDailySummaries.push(...dailySummaries);
        } catch (error) {
          console.error(`❌ Error processing ${ecgFile}:`, error.message);
        }
      }
    }
  }

  // Import all daily summaries
  if (allDailySummaries.length > 0) {
    await importDailyECGSummaries(allDailySummaries);
  }

  return allDailySummaries.length;
}

// Generate summary statistics
async function generateSummaryStatistics() {
  const session = driver.session({ database: NEO4J_DATABASE });

  try {
    console.log('📈 Generating summary statistics...');

    const result = await session.run(`
      MATCH (p:D1NAMOSubject)
      OPTIONAL MATCH (p)-[:HAS_DAILY_ECG_SUMMARY]->(d:DailyECGSummary)
      RETURN
        count(DISTINCT p) as totalPatients,
        count(d) as totalDailySummaries,
        sum(d.readingCount) as totalOriginalReadings,
        avg(d.estimatedHeartRate) as avgEstimatedHeartRate,
        avg(d.ecgMean) as avgECGValue,
        min(d.date) as earliestDate,
        max(d.date) as latestDate
    `);

    const stats = result.records[0].toObject();

    console.log('📊 D1NAMO Aggregated Import Summary:');
    console.log(`   Patients: ${stats.totalPatients}`);
    console.log(`   Daily Summaries: ${stats.totalDailySummaries}`);
    console.log(`   Original Readings Summarized: ${stats.totalOriginalReadings}`);
    console.log(`   Average Estimated Heart Rate: ${stats.avgEstimatedHeartRate ? Math.round(stats.avgEstimatedHeartRate) : 'N/A'} BPM`);
    console.log(`   Average ECG Value: ${stats.avgECGValue ? Math.round(stats.avgECGValue) : 'N/A'}`);
    console.log(`   Date Range: ${stats.earliestDate} to ${stats.latestDate}`);

    return stats;

  } catch (error) {
    console.error('❌ Error generating statistics:', error);
    throw error;
  } finally {
    await session.close();
  }
}

// Main import function
async function importAggregatedD1NAMODataset() {
  console.log('🚀 Starting Aggregated D1NAMO Dataset Import...');
  console.log(`📂 Dataset Path: ${DATASET_PATH}`);
  console.log(`🔍 Checking if path exists: ${fs.existsSync(DATASET_PATH)}`);

  try {
    // Setup database
    await createConstraintsAndIndexes();

    // Import aggregated ECG data
    const totalSummaries = await scanAndImportAggregatedECGFiles();

    // Generate summary
    await generateSummaryStatistics();

    console.log(`✅ Aggregated D1NAMO import completed successfully!`);
    console.log(`📊 Total daily summaries created: ${totalSummaries}`);

    return true;

  } catch (error) {
    console.error('❌ Aggregated D1NAMO import failed:', error);
    throw error;
  }
}

// Export functions
export { importAggregatedD1NAMODataset, PATIENT_METADATA, processECGFileAggregated };

// Run if called directly
if (import.meta.url === `file://${process.argv[1].replace(/\\/g, '/')}` || process.argv[1].endsWith('importAggregatedD1NAMODataset.js')) {
  console.log('🚀 Starting aggregated D1NAMO import as main script...');
  importAggregatedD1NAMODataset()
    .then(() => {
      console.log('🎉 Aggregated import script completed successfully.');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Aggregated import script failed:', error);
      console.error('Error details:', error.stack);
      process.exit(1);
    })
    .finally(() => {
      driver.close();
    });
}
