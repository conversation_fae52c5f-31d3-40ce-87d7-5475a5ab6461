# Local server-only secrets for Cloudflare Pages Functions
# Copy this file to .dev.vars and fill in your actual values
# This file is used by Wrangler for local development

# Neo4j Database Configuration (Server-side)
# These credentials are used by the /api/neo4j endpoint
NEO4J_URI=neo4j+s://your-instance.databases.neo4j.io
NEO4J_USERNAME=neo4j
NEO4J_PASSWORD=your-neo4j-password
NEO4J_DATABASE=neo4j

# OpenAI API Configuration (Server-side)
# Used by the /api/ai endpoint for AI-powered recommendations
OPENAI_API_KEY=sk-your-openai-api-key-here

# Optional Development Settings
NODE_ENV=development

# Security Notes:
# - Never commit .dev.vars to version control
# - These secrets are only available to server-side functions
# - For production, set these as Cloudflare Pages environment variables
# - Use 'wrangler pages secret put' command for production secrets

# Example production secret setup:
# wrangler pages secret put NEO4J_PASSWORD --project-name=healthhub-research-platform
# wrangler pages secret put OPENAI_API_KEY --project-name=healthhub-research-platform
