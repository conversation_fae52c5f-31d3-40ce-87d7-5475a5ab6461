// Test setup file for Vitest
import { vi } from 'vitest';

// Mock environment variables - override any existing ones
const testEnv = {
  VITE_NEO4J_URI: 'bolt://localhost:7687',
  VITE_NEO4J_USERNAME: 'neo4j',
  VITE_NEO4J_PASSWORD: 'testpassword',
  VITE_NEO4J_DATABASE: 'neo4j',
  VITE_NEO4J_USE_API: 'false'
};

// Override import.meta.env completely
Object.defineProperty(import.meta, 'env', {
  value: testEnv,
  writable: true,
  configurable: true
});

// Also set process.env for Node.js compatibility
Object.assign(process.env, testEnv);

// Mock fetch globally
global.fetch = vi.fn();

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  log: vi.fn(),
  warn: vi.fn(),
  error: vi.fn()
};

// Mock URL and Blob for file operations
global.URL = {
  createObjectURL: vi.fn(() => 'mock-url'),
  revokeObjectURL: vi.fn()
};

global.Blob = vi.fn();

// Mock FileReader
global.FileReader = vi.fn(() => ({
  readAsText: vi.fn(),
  onload: null,
  onerror: null
}));

// Reset all mocks before each test
beforeEach(() => {
  vi.clearAllMocks();
});
