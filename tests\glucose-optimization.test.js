/**
 * Comprehensive test suite for glucose data optimization
 * Tests the new optimized Neo4j endpoint and glucose data service
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import glucoseDataService from '../src/services/glucoseDataService.js';

// Mock fetch for testing
global.fetch = vi.fn();

describe('Glucose Data Optimization', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    vi.clearAllMocks();
    glucoseDataService.clearCache();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('GlucoseDataService', () => {
    it('should initialize with correct default values', () => {
      const metrics = glucoseDataService.getMetrics();
      
      expect(metrics.totalRequests).toBe(0);
      expect(metrics.cacheHits).toBe(0);
      expect(metrics.errors).toBe(0);
      expect(metrics.cacheSize).toBe(0);
    });

    it('should generate correct cache keys', () => {
      const cacheKey1 = glucoseDataService.getCacheKey('glucose_recent', { patientId: 'P1', days: 7 });
      const cacheKey2 = glucoseDataService.getCacheKey('glucose_recent', { patientId: 'P1', days: 14 });
      const cacheKey3 = glucoseDataService.getCacheKey('glucose_stats', { patientId: 'P1', days: 7 });
      
      expect(cacheKey1).toBe('glucose_recent_{"patientId":"P1","days":7}');
      expect(cacheKey2).toBe('glucose_recent_{"patientId":"P1","days":14}');
      expect(cacheKey3).toBe('glucose_stats_{"patientId":"P1","days":7}');
      expect(cacheKey1).not.toBe(cacheKey2);
      expect(cacheKey1).not.toBe(cacheKey3);
    });

    it('should cache and retrieve data correctly', () => {
      const testData = { records: [{ glucose: 120, timestamp: '2023-01-01' }] };
      const cacheKey = 'test_key';
      
      // Initially no cached data
      expect(glucoseDataService.getCachedData(cacheKey)).toBeNull();
      
      // Cache data
      glucoseDataService.setCachedData(cacheKey, testData);
      
      // Should retrieve cached data
      const cached = glucoseDataService.getCachedData(cacheKey);
      expect(cached).toEqual(testData);
      
      // Metrics should reflect cache usage
      const metrics = glucoseDataService.getMetrics();
      expect(metrics.cacheSize).toBe(1);
    });

    it('should handle cache expiration', async () => {
      const testData = { records: [{ glucose: 120 }] };
      const cacheKey = 'test_key';
      
      // Mock cache timeout to be very short
      const originalTimeout = glucoseDataService.cacheTimeout;
      glucoseDataService.cacheTimeout = 1; // 1ms
      
      glucoseDataService.setCachedData(cacheKey, testData);
      
      // Wait for cache to expire
      await new Promise(resolve => setTimeout(resolve, 10));
      
      // Should return null for expired cache
      expect(glucoseDataService.getCachedData(cacheKey)).toBeNull();
      
      // Restore original timeout
      glucoseDataService.cacheTimeout = originalTimeout;
    });

    it('should execute optimized queries with caching', async () => {
      const mockResponse = {
        records: [
          { 'g.timestamp': '2023-01-01T10:00:00Z', 'g.glucose': 120 },
          { 'g.timestamp': '2023-01-01T11:00:00Z', 'g.glucose': 130 }
        ],
        optimized: true,
        responseTime: 150
      };

      fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse
      });

      const result = await glucoseDataService.getRecentGlucoseReadings('Patient_1', 7, 100);
      
      expect(fetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/neo4j-optimized'),
        expect.objectContaining({
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            queryType: 'glucose_recent',
            parameters: { patientId: 'Patient_1', days: 7, limit: 100 },
            optimized: true
          })
        })
      );

      expect(result).toEqual(mockResponse);
      
      // Second call should use cache
      const cachedResult = await glucoseDataService.getRecentGlucoseReadings('Patient_1', 7, 100);
      expect(cachedResult).toEqual(mockResponse);
      
      // Should have made two API calls (one for each request)
      expect(fetch).toHaveBeenCalledTimes(2);
      
      // Metrics should show cache hit
      const metrics = glucoseDataService.getMetrics();
      expect(metrics.totalRequests).toBe(2);
      expect(metrics.cacheHits).toBe(1);
    });

    it('should handle API errors gracefully', async () => {
      fetch.mockRejectedValueOnce(new Error('Network error'));

      await expect(
        glucoseDataService.getRecentGlucoseReadings('Patient_1', 7, 100)
      ).rejects.toThrow('Network error');

      const metrics = glucoseDataService.getMetrics();
      expect(metrics.errors).toBe(1);
    });

    it('should load comprehensive dashboard data', async () => {
      const mockResponses = [
        { records: [{ 'g.glucose': 120 }] }, // recent readings
        { records: [{ avgGlucose: 125, minGlucose: 80, maxGlucose: 200 }] }, // statistics
        { records: [{ minuteOfDay: 600, glucose: 120 }] }, // AGP data
        { records: [{ timeInRangePercent: 75.5, totalReadings: 100 }] } // time in range
      ];

      // Mock multiple fetch calls
      mockResponses.forEach(response => {
        fetch.mockResolvedValueOnce({
          ok: true,
          json: async () => response
        });
      });

      const dashboardData = await glucoseDataService.getGlucoseDashboardData('Patient_1', 30);

      expect(dashboardData).toHaveProperty('recentReadings');
      expect(dashboardData).toHaveProperty('statistics');
      expect(dashboardData).toHaveProperty('agpData');
      expect(dashboardData).toHaveProperty('timeInRange');
      expect(dashboardData).toHaveProperty('metadata');
      
      expect(dashboardData.metadata.patientId).toBe('Patient_1');
      expect(dashboardData.metadata.days).toBe(30);
      
      // Should have made 4 API calls (parallel execution)
      expect(fetch).toHaveBeenCalledTimes(4);
    });

    it('should perform health checks', async () => {
      const mockHealthResponse = {
        healthy: true,
        timestamp: '2023-01-01T10:00:00Z',
        responseTime: 100
      };

      fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockHealthResponse
      });

      const health = await glucoseDataService.healthCheck();

      expect(health.healthy).toBe(true);
      expect(health.service).toBe('GlucoseDataService');
      expect(health.backend).toEqual(mockHealthResponse);
    });

    it('should handle fallback to regular API', async () => {
      const mockFallbackResponse = {
        records: [{ timestamp: '2023-01-01T10:00:00Z', glucose: 120 }]
      };

      // First call fails (optimized endpoint)
      fetch.mockRejectedValueOnce(new Error('Optimized endpoint not available'));
      
      // Second call succeeds (fallback)
      fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockFallbackResponse
      });

      const result = await glucoseDataService.getGlucoseReadingsWithFallback('Patient_1', 7, 100);

      expect(result).toEqual(mockFallbackResponse);
      expect(fetch).toHaveBeenCalledTimes(2);
      
      // First call to optimized endpoint
      expect(fetch).toHaveBeenNthCalledWith(1, 
        expect.stringContaining('/api/neo4j-optimized'),
        expect.any(Object)
      );
      
      // Second call to regular endpoint
      expect(fetch).toHaveBeenNthCalledWith(2,
        expect.stringContaining('/api/neo4j'),
        expect.any(Object)
      );
    });

    it('should clear cache correctly', () => {
      // Add some cached data
      glucoseDataService.setCachedData('key1', { data: 'test1' });
      glucoseDataService.setCachedData('key2', { data: 'test2' });
      
      let metrics = glucoseDataService.getMetrics();
      expect(metrics.cacheSize).toBe(2);
      
      // Clear cache
      glucoseDataService.clearCache();
      
      metrics = glucoseDataService.getMetrics();
      expect(metrics.cacheSize).toBe(0);
      
      // Should not find cached data
      expect(glucoseDataService.getCachedData('key1')).toBeNull();
      expect(glucoseDataService.getCachedData('key2')).toBeNull();
    });

    it('should calculate cache hit rate correctly', () => {
      // Simulate some requests
      glucoseDataService.metrics.totalRequests = 10;
      glucoseDataService.metrics.cacheHits = 3;
      
      const metrics = glucoseDataService.getMetrics();
      expect(metrics.cacheHitRate).toBe('30.0%');
    });

    it('should limit cache size', () => {
      // Add more than 50 items to trigger cache cleanup
      for (let i = 0; i < 55; i++) {
        glucoseDataService.setCachedData(`key${i}`, { data: `test${i}` });
      }
      
      const metrics = glucoseDataService.getMetrics();
      expect(metrics.cacheSize).toBeLessThanOrEqual(50);
    });
  });

  describe('Performance Metrics', () => {
    it('should track response times correctly', async () => {
      const mockResponse = { records: [] };
      
      fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => {
          // Simulate some delay
          await new Promise(resolve => setTimeout(resolve, 100));
          return mockResponse;
        }
      });

      const startTime = Date.now();
      await glucoseDataService.getRecentGlucoseReadings('Patient_1', 7, 100);
      const endTime = Date.now();
      
      const metrics = glucoseDataService.getMetrics();
      expect(metrics.avgResponseTime).toBeGreaterThan(0);
      expect(metrics.avgResponseTime).toBeLessThan(endTime - startTime + 50); // Allow some margin
    });

    it('should track error counts', async () => {
      fetch.mockRejectedValueOnce(new Error('Test error'));
      
      try {
        await glucoseDataService.getRecentGlucoseReadings('Patient_1', 7, 100);
      } catch (error) {
        // Expected to throw
      }
      
      const metrics = glucoseDataService.getMetrics();
      expect(metrics.errors).toBeGreaterThanOrEqual(1);
    });
  });
});

// Integration test for the optimized endpoint (requires actual deployment)
describe('Neo4j Optimized Endpoint Integration', () => {
  it('should respond to health checks', async () => {
    // This test would run against the actual deployed endpoint
    // Skip in CI/CD unless endpoint is available
    if (process.env.SKIP_INTEGRATION_TESTS) {
      return;
    }

    const response = await fetch('https://healthhub-research-platform.pages.dev/api/neo4j-optimized');
    expect(response.ok).toBe(true);
    
    const health = await response.json();
    expect(health).toHaveProperty('healthy');
    expect(health).toHaveProperty('timestamp');
  });
});
