import dotenv from 'dotenv';
import neo4j from 'neo4j-driver';

dotenv.config();

async function verifyImportedData() {
  let driver = null;

  try {
    console.log('🔍 Verifying imported D1NAMO data...');

    const uri = process.env.VITE_NEO4J_URI;
    const username = process.env.VITE_NEO4J_USERNAME;
    const password = process.env.VITE_NEO4J_PASSWORD;

    // Create driver and test connection
    driver = neo4j.driver(uri, neo4j.auth.basic(username, password));
    const session = driver.session();

    try {
      // Check all node types
      console.log('\n📊 Database Overview:');
      const overviewResult = await session.run(`
        MATCH (n)
        RETURN labels(n)[0] as nodeType, count(n) as count
        ORDER BY nodeType
      `);

      overviewResult.records.forEach(record => {
        const nodeType = record.get('nodeType');
        const count = record.get('count').toNumber();
        console.log(`   ${nodeType}: ${count} nodes`);
      });

      // Check D1NAMO patients with ECG data
      console.log('\n👥 D1NAMO Patients with Data:');
      const patientsResult = await session.run(`
        MATCH (p:Patient:D1NAMOSubject)
        OPTIONAL MATCH (p)-[:HAD_ECG]->(e:ECGReading)
        OPTIONAL MATCH (p)-[:HAD_READING]->(g:GlucoseReading)
        OPTIONAL MATCH (e)-[:HAS_FEATURES]->(f:ECGFeatures)
        RETURN p.patientId as patientId,
               p.name as name,
               p.condition as condition,
               count(DISTINCT e) as ecgCount,
               count(DISTINCT g) as glucoseCount,
               count(DISTINCT f) as featuresCount
        ORDER BY p.patientId
      `);

      console.log('┌─────────┬──────────────────────┬─────────────────┬─────────┬─────────┬──────────┐');
      console.log('│ Patient │ Name                 │ Condition       │ ECG     │ Glucose │ Features │');
      console.log('├─────────┼──────────────────────┼─────────────────┼─────────┼─────────┼──────────┤');

      let totalECG = 0;
      let totalGlucose = 0;
      let totalFeatures = 0;

      patientsResult.records.forEach(record => {
        const patientId = record.get('patientId') || 'N/A';
        const name = (record.get('name') || 'Unknown').substring(0, 20);
        const condition = (record.get('condition') || 'Unknown').substring(0, 15);
        const ecgCount = record.get('ecgCount').toNumber();
        const glucoseCount = record.get('glucoseCount').toNumber();
        const featuresCount = record.get('featuresCount').toNumber();

        totalECG += ecgCount;
        totalGlucose += glucoseCount;
        totalFeatures += featuresCount;

        console.log(`│ ${patientId.toString().padEnd(7)} │ ${name.padEnd(20)} │ ${condition.padEnd(15)} │ ${ecgCount.toString().padStart(7)} │ ${glucoseCount.toString().padStart(7)} │ ${featuresCount.toString().padStart(8)} │`);
      });

      console.log('├─────────┼──────────────────────┼─────────────────┼─────────┼─────────┼──────────┤');
      console.log(`│ TOTAL   │                      │                 │ ${totalECG.toString().padStart(7)} │ ${totalGlucose.toString().padStart(7)} │ ${totalFeatures.toString().padStart(8)} │`);
      console.log('└─────────┴──────────────────────┴─────────────────┴─────────┴─────────┴──────────┘');

      // Check sample ECG readings
      if (totalECG > 0) {
        console.log('\n💓 Sample ECG Readings:');
        const ecgSampleResult = await session.run(`
          MATCH (p:Patient)-[:HAD_ECG]->(e:ECGReading)
          OPTIONAL MATCH (e)-[:HAS_FEATURES]->(f:ECGFeatures)
          RETURN p.patientId as patientId,
                 e.readingId as readingId,
                 e.timestamp as timestamp,
                 e.duration as duration,
                 f.heartRate as heartRate,
                 f.hrv_rmssd as hrv
          ORDER BY e.timestamp DESC
          LIMIT 5
        `);

        ecgSampleResult.records.forEach((record, i) => {
          const patientId = record.get('patientId');
          const timestamp = new Date(record.get('timestamp')).toLocaleString();
          const heartRate = record.get('heartRate');
          const hrv = record.get('hrv_rmssd');
          console.log(`   ${i + 1}. Patient ${patientId} - ${timestamp} - HR: ${heartRate} bpm, HRV: ${hrv} ms`);
        });
      }

      // Check sample glucose readings
      if (totalGlucose > 0) {
        console.log('\n🍯 Sample Glucose Readings:');
        const glucoseSampleResult = await session.run(`
          MATCH (p:Patient)-[:HAD_READING]->(g:GlucoseReading)
          RETURN p.patientId as patientId,
                 g.timestamp as timestamp,
                 g.value as value,
                 g.readingType as readingType
          ORDER BY g.timestamp DESC
          LIMIT 5
        `);

        glucoseSampleResult.records.forEach((record, i) => {
          const patientId = record.get('patientId');
          const timestamp = new Date(record.get('timestamp')).toLocaleString();
          const value = record.get('value');
          const readingType = record.get('readingType');
          console.log(`   ${i + 1}. Patient ${patientId} - ${timestamp} - ${value} mg/dL (${readingType})`);
        });
      }

      console.log(`\n✅ Import Verification Complete!`);
      console.log(`   📊 ${patientsResult.records.length} D1NAMO patients found`);
      console.log(`   💓 ${totalECG} ECG readings with ${totalFeatures} feature analyses`);
      console.log(`   🍯 ${totalGlucose} glucose readings generated`);

      if (totalECG === 0) {
        console.log('\n⚠️  Warning: No ECG readings found. Check patient ID matching in import script.');
      }

    } finally {
      await session.close();
    }

  } catch (error) {
    console.error('❌ Verification failed:', error.message);
  } finally {
    if (driver) {
      await driver.close();
    }
  }
}

verifyImportedData();
