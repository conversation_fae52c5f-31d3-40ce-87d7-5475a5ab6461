#!/usr/bin/env node

/**
 * Quick Start Script for HealthHub Research Platform
 * 
 * This script provides a guided setup experience for new developers
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { execSync } from 'child_process';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.join(__dirname, '..');

const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logStep(step, message) {
  log(`\n[${step}] ${message}`, 'cyan');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

async function checkPrerequisites() {
  logStep('1/6', 'Checking Prerequisites');

  // Check Node.js version
  try {
    const nodeVersion = process.version;
    const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
    
    if (majorVersion >= 16) {
      logSuccess(`Node.js ${nodeVersion} is supported`);
    } else {
      logError(`Node.js ${nodeVersion} is too old. Please upgrade to Node.js 16 or later.`);
      return false;
    }
  } catch (error) {
    logError('Could not determine Node.js version');
    return false;
  }

  // Check npm
  try {
    const npmVersion = execSync('npm --version', { encoding: 'utf8' }).trim();
    logSuccess(`npm ${npmVersion} is available`);
  } catch (error) {
    logError('npm is not available');
    return false;
  }

  return true;
}

async function installDependencies() {
  logStep('2/6', 'Installing Dependencies');

  try {
    // Check if node_modules exists
    const nodeModulesPath = path.join(rootDir, 'node_modules');
    const packageLockPath = path.join(rootDir, 'package-lock.json');
    
    if (fs.existsSync(nodeModulesPath) && fs.existsSync(packageLockPath)) {
      logSuccess('Dependencies already installed');
      return true;
    }

    log('Installing npm packages...', 'blue');
    execSync('npm install', { 
      cwd: rootDir, 
      stdio: 'inherit'
    });
    
    logSuccess('Dependencies installed successfully');
    return true;
  } catch (error) {
    logError(`Failed to install dependencies: ${error.message}`);
    return false;
  }
}

async function setupEnvironmentFiles() {
  logStep('3/6', 'Setting Up Environment Files');

  const envPath = path.join(rootDir, '.env');
  const devVarsPath = path.join(rootDir, '.dev.vars');
  const envExamplePath = path.join(rootDir, '.env.example');
  const devVarsExamplePath = path.join(rootDir, '.dev.vars.example');

  // Setup .env file
  if (!fs.existsSync(envPath)) {
    if (fs.existsSync(envExamplePath)) {
      fs.copyFileSync(envExamplePath, envPath);
      logSuccess('Created .env from .env.example');
      logWarning('Please edit .env with your actual configuration values');
    } else {
      logError('.env.example not found');
      return false;
    }
  } else {
    logSuccess('.env file already exists');
  }

  // Setup .dev.vars file
  if (!fs.existsSync(devVarsPath)) {
    if (fs.existsSync(devVarsExamplePath)) {
      fs.copyFileSync(devVarsExamplePath, devVarsPath);
      logSuccess('Created .dev.vars from .dev.vars.example');
      logWarning('Please edit .dev.vars with your actual server-side secrets');
    } else {
      logError('.dev.vars.example not found');
      return false;
    }
  } else {
    logSuccess('.dev.vars file already exists');
  }

  return true;
}

async function validateConfiguration() {
  logStep('4/6', 'Validating Configuration');

  try {
    // Import and run the validation script
    const { default: validateEnv } = await import('./validate-env.js');
    
    // Capture console output to check if validation passed
    let validationPassed = true;
    const originalExit = process.exit;
    process.exit = (code) => {
      if (code !== 0) validationPassed = false;
    };

    try {
      execSync('node scripts/validate-env.js', { 
        cwd: rootDir, 
        stdio: 'inherit'
      });
    } catch (error) {
      validationPassed = false;
    }

    process.exit = originalExit;

    if (validationPassed) {
      logSuccess('Environment configuration is valid');
      return true;
    } else {
      logWarning('Environment configuration needs attention');
      log('Please review and fix the configuration issues above', 'yellow');
      return false;
    }
  } catch (error) {
    logError(`Configuration validation failed: ${error.message}`);
    return false;
  }
}

async function testConnections() {
  logStep('5/6', 'Testing External Connections');

  try {
    execSync('node scripts/setup-local-dev.js', { 
      cwd: rootDir, 
      stdio: 'inherit'
    });
    logSuccess('Connection tests completed');
    return true;
  } catch (error) {
    logWarning('Some connection tests may have failed');
    log('Check the output above for details', 'yellow');
    return true; // Don't fail the setup for connection issues
  }
}

async function startDevelopment() {
  logStep('6/6', 'Starting Development Server');

  log('\n🚀 Your development environment is ready!', 'green');
  log('\nAvailable commands:', 'bright');
  log('  npm run dev:local  # Start both frontend and API server', 'cyan');
  log('  npm run dev        # Start frontend only', 'cyan');
  log('  npm run dev:api    # Start API server only', 'cyan');
  log('  npm run build      # Build for production', 'cyan');
  log('  npm run lint       # Check code quality', 'cyan');

  log('\n🔗 Local URLs:', 'bright');
  log('  Frontend: http://localhost:5173', 'cyan');
  log('  API: http://127.0.0.1:8787', 'cyan');
  log('  Health Check: http://127.0.0.1:8787/api/health', 'cyan');

  log('\n📚 Next Steps:', 'bright');
  log('  1. Review and update .env and .dev.vars with your credentials', 'yellow');
  log('  2. Run "npm run dev:local" to start development', 'yellow');
  log('  3. Open http://localhost:5173 in your browser', 'yellow');
  log('  4. Check the README.md for detailed documentation', 'yellow');

  // Ask if user wants to start the dev server
  log('\nWould you like to start the development server now? (y/N)', 'bright');
  
  // For automated setup, we'll skip the interactive prompt
  const args = process.argv.slice(2);
  if (args.includes('--auto-start')) {
    log('Starting development server...', 'green');
    try {
      execSync('npm run dev:local', { 
        cwd: rootDir, 
        stdio: 'inherit'
      });
    } catch (error) {
      logError('Failed to start development server');
      log('You can start it manually with: npm run dev:local', 'yellow');
    }
  }
}

async function main() {
  log('🏥 HealthHub Research Platform - Quick Start Setup', 'bright');
  log('This script will help you set up your development environment\n', 'blue');

  const steps = [
    checkPrerequisites,
    installDependencies,
    setupEnvironmentFiles,
    validateConfiguration,
    testConnections,
    startDevelopment
  ];

  for (const step of steps) {
    const success = await step();
    if (!success && step !== testConnections && step !== validateConfiguration) {
      logError('Setup failed. Please fix the issues above and try again.');
      process.exit(1);
    }
  }

  log('\n🎉 Setup completed successfully!', 'green');
}

if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}
