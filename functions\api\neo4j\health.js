/**
 * Neo4j Health Check Endpoint
 * GET /api/neo4j/health
 * Returns: { healthy: boolean, timestamp: string, details?: object }
 */

export async function onRequestGet({ env }) {
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type',
    'Content-Type': 'application/json'
  };

  try {
    // Check if Neo4j credentials are configured
    if (!env.NEO4J_PASSWORD) {
      return new Response(JSON.stringify({
        healthy: false,
        timestamp: new Date().toISOString(),
        error: 'Neo4j credentials not configured'
      }), {
        status: 503,
        headers
      });
    }

    // Dynamically import Neo4j driver
    const mod = await import('neo4j-driver');
    const neo4j = mod?.default || mod;

    const config = {
      uri: env.NEO4J_URI || 'bolt://localhost:7687',
      username: env.NEO4J_USERNAME || 'neo4j',
      password: env.NEO4J_PASSWORD,
      database: env.NEO4J_DATABASE || 'neo4j'
    };

    // Create driver and test connection
    const driver = neo4j.driver(
      config.uri,
      neo4j.auth.basic(config.username, config.password),
      { maxConnectionPoolSize: 1 }
    );

    const session = driver.session({ database: config.database });

    try {
      const startTime = Date.now();
      const result = await session.run('RETURN 1 as healthCheck, datetime() as timestamp');
      const responseTime = Date.now() - startTime;
      
      const record = result.records[0];
      const serverTime = record.get('timestamp').toString();

      return new Response(JSON.stringify({
        healthy: true,
        timestamp: new Date().toISOString(),
        serverTime,
        responseTime,
        database: config.database,
        uri: config.uri.replace(/\/\/.*@/, '//***@') // Hide credentials
      }), {
        status: 200,
        headers
      });

    } finally {
      await session.close();
      await driver.close();
    }

  } catch (error) {
    console.error('Neo4j health check failed:', error);
    
    return new Response(JSON.stringify({
      healthy: false,
      timestamp: new Date().toISOString(),
      error: error.message,
      code: error.code
    }), {
      status: 503,
      headers
    });
  }
}

export async function onRequestOptions() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type'
    }
  });
}
