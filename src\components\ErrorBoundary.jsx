import { Component } from 'react';
import './ErrorBoundary.css';

class ErrorBoundary extends Component {
    constructor(props) {
        super(props);
        this.state = { 
            hasError: false, 
            error: null, 
            errorInfo: null,
            errorId: null
        };
    }

    static getDerivedStateFromError(error) {
        // Update state so the next render will show the fallback UI
        return { 
            hasError: true,
            errorId: Date.now().toString(36) + Math.random().toString(36).substr(2)
        };
    }

    componentDidCatch(error, errorInfo) {
        // Log error details
        console.error('ErrorBoundary caught an error:', error, errorInfo);
        
        this.setState({
            error: error,
            errorInfo: errorInfo
        });

        // Report error to monitoring service if available
        if (window.reportError) {
            window.reportError(error, errorInfo);
        }
    }

    handleRetry = () => {
        this.setState({ 
            hasError: false, 
            error: null, 
            errorInfo: null,
            errorId: null
        });
    };

    render() {
        if (this.state.hasError) {
            const { fallback: Fallback, title = "Something went wrong" } = this.props;
            
            if (Fallback) {
                return <Fallback 
                    error={this.state.error} 
                    errorInfo={this.state.errorInfo}
                    onRetry={this.handleRetry}
                />;
            }

            return (
                <div className="error-boundary">
                    <div className="error-boundary-content">
                        <div className="error-icon">⚠️</div>
                        <h2>{title}</h2>
                        <p>An unexpected error occurred while rendering this component.</p>
                        
                        {this.state.error && (
                            <details className="error-details">
                                <summary>Error Details (ID: {this.state.errorId})</summary>
                                <div className="error-message">
                                    <strong>Error:</strong> {this.state.error.toString()}
                                </div>
                                {this.state.errorInfo && (
                                    <div className="error-stack">
                                        <strong>Component Stack:</strong>
                                        <pre>{this.state.errorInfo.componentStack}</pre>
                                    </div>
                                )}
                            </details>
                        )}
                        
                        <div className="error-actions">
                            <button 
                                onClick={this.handleRetry}
                                className="retry-button"
                            >
                                🔄 Try Again
                            </button>
                            <button 
                                onClick={() => window.location.reload()}
                                className="reload-button"
                            >
                                🔄 Reload Page
                            </button>
                        </div>
                        
                        <div className="error-help">
                            <p>If this problem persists, please:</p>
                            <ul>
                                <li>Check your internet connection</li>
                                <li>Clear your browser cache</li>
                                <li>Contact support with Error ID: {this.state.errorId}</li>
                            </ul>
                        </div>
                    </div>
                </div>
            );
        }

        return this.props.children;
    }
}

export default ErrorBoundary;
