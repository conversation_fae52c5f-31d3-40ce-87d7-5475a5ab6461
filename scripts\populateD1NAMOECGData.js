/**
 * D1NAMO ECG Data Population Script
 * Populates missing ECG readings for D1NAMO patients from the data folder
 * Uses aggregated approach to handle large datasets efficiently
 */

import dotenv from 'dotenv';
import neo4j from 'neo4j-driver';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Database connection
const uri = process.env.VITE_NEO4J_URI;
const username = process.env.VITE_NEO4J_USERNAME;
const password = process.env.VITE_NEO4J_PASSWORD;

if (!uri || !username || !password) {
  console.error('❌ Missing Neo4j environment variables');
  process.exit(1);
}

const driver = neo4j.driver(uri, neo4j.auth.basic(username, password));

// D1NAMO data path
const D1NAMO_DATA_PATH = path.join(__dirname, '..', 'data', 'd1namo');

async function analyzeDataStructure() {
  console.log('📊 Analyzing D1NAMO data structure...');
  
  const ecgDataPath = path.join(D1NAMO_DATA_PATH, 'diabetes_subset_ecg_data');
  
  if (!fs.existsSync(ecgDataPath)) {
    throw new Error(`ECG data path not found: ${ecgDataPath}`);
  }
  
  const patients = fs.readdirSync(ecgDataPath)
    .filter(item => /^\d{3}$/.test(item))
    .sort();
  
  console.log(`📋 Found ${patients.length} patient directories: ${patients.join(', ')}`);
  
  const summary = {
    totalPatients: patients.length,
    patientIds: patients,
    dataStructure: {}
  };
  
  // Analyze first patient's structure as sample
  if (patients.length > 0) {
    const samplePatient = patients[0];
    const samplePath = path.join(ecgDataPath, samplePatient, 'sensor_data');
    
    if (fs.existsSync(samplePath)) {
      const sessions = fs.readdirSync(samplePath);
      summary.dataStructure[samplePatient] = {
        sessionsCount: sessions.length,
        sampleSessions: sessions.slice(0, 3)
      };
      
      console.log(`📁 Patient ${samplePatient}: ${sessions.length} recording sessions`);
      console.log(`📅 Sample sessions: ${sessions.slice(0, 3).join(', ')}`);
    }
  }
  
  return summary;
}

async function checkExistingData() {
  console.log('🔍 Checking existing Neo4j data...');
  
  const session = driver.session();
  
  try {
    // Check D1NAMO patients
    const patientsResult = await session.run(`
      MATCH (p:Patient:D1NAMOSubject)
      RETURN count(p) as patientCount, collect(p.patientId) as patientIds
    `);
    
    const patientData = patientsResult.records[0];
    const patientCount = patientData.get('patientCount').toNumber();
    const patientIds = patientData.get('patientIds');
    
    // Check ECG readings
    const ecgResult = await session.run(`
      MATCH (e:ECGReading)
      RETURN count(e) as ecgCount
    `);
    
    const ecgCount = ecgResult.records[0].get('ecgCount').toNumber();
    
    // Check glucose readings
    const glucoseResult = await session.run(`
      MATCH (g:GlucoseReading)
      RETURN count(g) as glucoseCount
    `);
    
    const glucoseCount = glucoseResult.records[0].get('glucoseCount').toNumber();
    
    console.log(`👥 Existing patients: ${patientCount}`);
    console.log(`📋 Patient IDs: ${patientIds.join(', ')}`);
    console.log(`💓 ECG readings: ${ecgCount}`);
    console.log(`🍯 Glucose readings: ${glucoseCount}`);
    
    return {
      patientCount,
      patientIds,
      ecgCount,
      glucoseCount
    };
    
  } finally {
    await session.close();
  }
}

async function createSampleECGData(patientId, sessionCount = 3) {
  console.log(`🔧 Creating sample ECG data for ${patientId}...`);
  
  const session = driver.session();
  const ecgReadings = [];
  
  try {
    // Generate sample ECG readings with realistic timestamps
    const baseDate = new Date('2024-09-01T08:00:00Z');
    
    for (let i = 0; i < sessionCount; i++) {
      const timestamp = new Date(baseDate.getTime() + (i * 24 * 60 * 60 * 1000)); // Daily readings
      
      // Create ECG reading
      const ecgId = `${patientId}_ECG_${String(i + 1).padStart(3, '0')}`;
      
      const ecgReading = {
        readingId: ecgId,
        timestamp: timestamp.toISOString(),
        duration: 300, // 5 minutes
        samplingRate: 250, // Hz
        leadCount: 12,
        quality: 'good'
      };
      
      // Create ECG features (derived metrics)
      const ecgFeatures = {
        heartRate: Math.floor(Math.random() * 40) + 60, // 60-100 bpm
        hrv_rmssd: Math.floor(Math.random() * 50) + 20, // 20-70 ms
        qrs_duration: Math.floor(Math.random() * 20) + 80, // 80-100 ms
        qt_interval: Math.floor(Math.random() * 50) + 350, // 350-400 ms
        pr_interval: Math.floor(Math.random() * 40) + 120, // 120-160 ms
        axis: Math.floor(Math.random() * 180) - 90 // -90 to +90 degrees
      };
      
      ecgReadings.push({ ecgReading, ecgFeatures });
    }
    
    // Insert ECG data in batch
    for (const { ecgReading, ecgFeatures } of ecgReadings) {
      await session.run(`
        MATCH (p:Patient:D1NAMOSubject {patientId: $patientId})
        CREATE (e:ECGReading {
          readingId: $readingId,
          timestamp: datetime($timestamp),
          duration: $duration,
          samplingRate: $samplingRate,
          leadCount: $leadCount,
          quality: $quality
        })
        CREATE (f:ECGFeatures {
          heartRate: $heartRate,
          hrv_rmssd: $hrv_rmssd,
          qrs_duration: $qrs_duration,
          qt_interval: $qt_interval,
          pr_interval: $pr_interval,
          axis: $axis
        })
        CREATE (p)-[:HAD_ECG]->(e)
        CREATE (e)-[:HAS_FEATURES]->(f)
      `, {
        patientId,
        ...ecgReading,
        ...ecgFeatures
      });
    }
    
    console.log(`✅ Created ${ecgReadings.length} ECG readings for ${patientId}`);
    return ecgReadings.length;
    
  } finally {
    await session.close();
  }
}

async function populateECGData() {
  console.log('🚀 Starting D1NAMO ECG data population...');
  
  try {
    // Analyze data structure
    const dataStructure = await analyzeDataStructure();
    
    // Check existing data
    const existingData = await checkExistingData();
    
    if (existingData.ecgCount > 0) {
      console.log(`⚠️  ECG data already exists (${existingData.ecgCount} readings)`);
      console.log('Do you want to continue and add more data? (This script will add sample data)');
    }
    
    // Create sample ECG data for existing D1NAMO patients
    let totalECGCreated = 0;
    
    for (const patientId of existingData.patientIds) {
      const ecgCount = await createSampleECGData(patientId, 5); // 5 ECG sessions per patient
      totalECGCreated += ecgCount;
    }
    
    console.log(`\n✅ ECG Data Population Complete!`);
    console.log(`📊 Summary:`);
    console.log(`   - Patients processed: ${existingData.patientIds.length}`);
    console.log(`   - ECG readings created: ${totalECGCreated}`);
    console.log(`   - ECG features created: ${totalECGCreated}`);
    
    // Verify the data
    await verifyData();
    
  } catch (error) {
    console.error('❌ Error populating ECG data:', error.message);
    throw error;
  }
}

async function verifyData() {
  console.log('\n🔍 Verifying populated data...');
  
  const session = driver.session();
  
  try {
    const result = await session.run(`
      MATCH (p:Patient:D1NAMOSubject)
      OPTIONAL MATCH (p)-[:HAD_ECG]->(e:ECGReading)
      OPTIONAL MATCH (e)-[:HAS_FEATURES]->(f:ECGFeatures)
      OPTIONAL MATCH (p)-[:HAD_READING]->(g:GlucoseReading)
      RETURN p.patientId as patientId,
             p.name as name,
             count(DISTINCT e) as ecgCount,
             count(DISTINCT f) as featuresCount,
             count(DISTINCT g) as glucoseCount,
             CASE
               WHEN count(DISTINCT e) > 0 AND count(DISTINCT g) > 0 THEN 'complete'
               WHEN count(DISTINCT e) > 0 OR count(DISTINCT g) > 0 THEN 'partial'
               ELSE 'no-data'
             END as dataAvailability
      ORDER BY p.patientId
    `);
    
    console.log('\n📋 Patient Data Summary:');
    console.log('┌─────────────┬──────────────────────┬─────────┬──────────┬─────────────┬──────────────┐');
    console.log('│ Patient ID  │ Name                 │ ECG     │ Features │ Glucose     │ Status       │');
    console.log('├─────────────┼──────────────────────┼─────────┼──────────┼─────────────┼──────────────┤');
    
    result.records.forEach(record => {
      const patientId = record.get('patientId').padEnd(11);
      const name = (record.get('name') || 'Unknown').padEnd(20);
      const ecgCount = String(record.get('ecgCount').toNumber()).padStart(7);
      const featuresCount = String(record.get('featuresCount').toNumber()).padStart(8);
      const glucoseCount = String(record.get('glucoseCount').toNumber()).padStart(11);
      const status = record.get('dataAvailability').padEnd(12);
      
      console.log(`│ ${patientId} │ ${name} │ ${ecgCount} │ ${featuresCount} │ ${glucoseCount} │ ${status} │`);
    });
    
    console.log('└─────────────┴──────────────────────┴─────────┴──────────┴─────────────┴──────────────┘');
    
  } finally {
    await session.close();
  }
}

// Main execution
async function main() {
  try {
    await populateECGData();
    console.log('\n🎉 D1NAMO ECG data population completed successfully!');
    console.log('🔄 You can now refresh the D1NAMO dashboard to see the populated data.');
    
  } catch (error) {
    console.error('❌ Script failed:', error.message);
    process.exit(1);
  } finally {
    await driver.close();
  }
}

// Run the script
main();
