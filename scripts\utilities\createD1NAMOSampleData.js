import dotenv from 'dotenv';
import neo4j from 'neo4j-driver';

dotenv.config();

const driver = neo4j.driver(
  process.env.VITE_NEO4J_URI,
  neo4j.auth.basic(process.env.VITE_NEO4J_USERNAME, process.env.VITE_NEO4J_PASSWORD)
);

async function createSampleECGData() {
  const session = driver.session();
  try {
    console.log('🚀 Creating sample ECG data for D1NAMO patients...');

    // Create ECG readings for first 3 patients
    const result = await session.run(`
      MATCH (p:D1NAMOSubject)
      WHERE p.patientId IN ['D1NAMO_001', 'D1NAMO_002', 'D1NAMO_003']
      WITH p
      UNWIND range(1, 5) as dayOffset
      CREATE (e:ECGReading:D1NAMOReading {
        readingId: 'ECG_' + substring(p.patientId, 7) + '_' + toString(dayOffset) + '_' + toString(toInteger(rand() * 1000)),
        timestamp: datetime({
          year: 2014,
          month: 10,
          day: 2 + dayOffset,
          hour: 8 + (dayOffset * 2) % 16,
          minute: toInteger(rand() * 60),
          second: toInteger(rand() * 60)
        }),
        duration: 10.0,
        samplingRate: 250,
        sampleCount: 2500,
        signalQuality: 'Good'
      })
      CREATE (f:ECGFeatures {
        heartRate: 60 + toInteger(rand() * 40),
        hrv_rmssd: 15 + toInteger(rand() * 30),
        meanAmplitude: 100 + toInteger(rand() * 150),
        stdDevAmplitude: 20 + toInteger(rand() * 20),
        qtc_interval: 380 + toInteger(rand() * 60),
        peakCount: 12 + toInteger(rand() * 15),
        signalQuality: 'Good'
      })
      CREATE (p)-[:HAD_ECG]->(e)
      CREATE (e)-[:HAS_FEATURES]->(f)
      RETURN p.name as patientName, count(e) as ecgCount
    `);

    console.log('✅ Created ECG data for patients:');
    result.records.forEach(record => {
      console.log(`   - ${record.get('patientName')}: ${record.get('ecgCount')} ECG readings`);
    });

    // Verify total counts
    const countResult = await session.run(`
      MATCH (p:D1NAMOSubject)
      OPTIONAL MATCH (p)-[:HAD_ECG]->(e:ECGReading)
      OPTIONAL MATCH (e)-[:HAS_FEATURES]->(f:ECGFeatures)
      RETURN
        count(DISTINCT p) as totalPatients,
        count(e) as totalReadings,
        count(f) as totalFeatures,
        avg(f.heartRate) as avgHeartRate
    `);

    const stats = countResult.records[0].toObject();
    console.log('📊 Final D1NAMO Status:');
    console.log(`   👥 Total Patients: ${stats.totalPatients}`);
    console.log(`   💓 Total ECG Readings: ${stats.totalReadings}`);
    console.log(`   📈 Total ECG Features: ${stats.totalFeatures}`);
    console.log(`   🫀 Average Heart Rate: ${stats.avgHeartRate ? Math.round(stats.avgHeartRate) : 'N/A'} BPM`);

    return true;
  } catch (error) {
    console.error('❌ Error creating sample data:', error);
    return false;
  } finally {
    await session.close();
  }
}

createSampleECGData()
  .then((success) => {
    console.log(success ? '🎉 Sample ECG data created successfully!' : '💥 Failed to create sample data');
    process.exit(success ? 0 : 1);
  })
  .finally(() => {
    driver.close();
  });
