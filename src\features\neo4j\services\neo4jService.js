/**
 * Complete Neo4j Service with Real Database Connectivity
 * Supports both real connections and mock data for development
 *
 * Features:
 * - Connection management with automatic fallback
 * - Query caching and performance monitoring
 * - Schema management and validation
 * - Health monitoring and metrics
 * - Security and rate limiting
 * - Export/import utilities
 */

class Neo4jService {
  constructor() {
    this.driver = null;
    this.neo4j = null; // will be set when driver is dynamically loaded
    this.driverLoaded = false;
    this.isConnected = false;
    this.connectionConfig = {
      uri: import.meta.env.VITE_NEO4J_URI || 'bolt://localhost:7687',
      username: import.meta.env.VITE_NEO4J_USERNAME || 'neo4j',
      password: import.meta.env.VITE_NEO4J_PASSWORD || '',
      database: import.meta.env.VITE_NEO4J_DATABASE || 'neo4j'
    };

    // Configuration options
    this.useApiProxy = import.meta.env.VITE_NEO4J_USE_API === 'true';
    this.lastError = null;
    this.queryCache = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
    this.useMockData = false;

    // Performance and monitoring
    this.queryStats = {
      totalQueries: 0,
      totalExecutionTime: 0,
      cacheHits: 0,
      avgExecutionTime: 0,
      slowQueries: [], // Track queries > 1000ms
      recentQueries: [], // Track last 10 queries
      errorCount: 0,
      lastError: null
    };

    // Rate limiting
    this.rateLimiter = {
      requests: [],
      maxRequests: 100, // per minute
      windowMs: 60 * 1000
    };

    // Health monitoring
    this.healthMetrics = {
      connectionUptime: 0,
      lastHealthCheck: null,
      isHealthy: false,
      consecutiveFailures: 0,
      maxConsecutiveFailures: 5
    };

    // Schema cache
    this.schemaCache = {
      nodeLabels: [],
      relationshipTypes: [],
      properties: {},
      indexes: [],
      constraints: [],
      lastUpdated: null
    };
  }

  /**
   * Validation and Security Methods
   */

  validateQuery(query) {
    if (!query || typeof query !== 'string') {
      throw new Error('Query must be a non-empty string');
    }

    // Check for dangerous operations
    const dangerousPatterns = [
      /\b(DROP|DELETE\s+ALL|DETACH\s+DELETE\s+ALL)\b/i,
      /\bCALL\s+db\.(createLabel|createRelationshipType|createProperty)\b/i,
      /\bCALL\s+dbms\.(shutdown|killConnection|killTransaction)\b/i,
      /\bLOAD\s+CSV\s+FROM\s+["'](?!file:\/\/\/)/i, // Only allow local files
      /\bAPOC\.(?!(?:toJSON|convert|map|coll|text|math|date|temporal|util\.md5|util\.sha1))/i
    ];

    for (const pattern of dangerousPatterns) {
      if (pattern.test(query)) {
        throw new Error(`Query contains potentially dangerous operation: ${pattern.source}`);
      }
    }

    // Check query length
    if (query.length > 10000) {
      throw new Error('Query too long (max 10,000 characters)');
    }

    return true;
  }

  validateParameters(parameters) {
    if (!parameters || typeof parameters !== 'object') {
      return {};
    }

    // Sanitize parameters
    const sanitized = {};
    for (const [key, value] of Object.entries(parameters)) {
      if (typeof key !== 'string' || key.length > 100) {
        throw new Error(`Invalid parameter key: ${key}`);
      }

      // Check for dangerous values
      if (typeof value === 'string' && value.length > 1000) {
        throw new Error(`Parameter value too long: ${key}`);
      }

      sanitized[key] = value;
    }

    return sanitized;
  }

  checkRateLimit() {
    const now = Date.now();

    // Remove old requests outside the window
    this.rateLimiter.requests = this.rateLimiter.requests.filter(
      timestamp => now - timestamp < this.rateLimiter.windowMs
    );

    // Check if we're over the limit
    if (this.rateLimiter.requests.length >= this.rateLimiter.maxRequests) {
      throw new Error('Rate limit exceeded. Please wait before making more requests.');
    }

    // Add current request
    this.rateLimiter.requests.push(now);
  }

  async loadNeo4j() {
    if (this.driverLoaded) return this.neo4j;
    try {
      // Dynamic import to avoid hard build-time dependency
      const mod = await import('neo4j-driver');
      this.neo4j = mod?.default || mod; // handle both default/named export
      this.driverLoaded = true;
      return this.neo4j;
    } catch (e) {
      // Driver not available (not installed or blocked). Fall back to mock mode.
      console.warn('neo4j-driver not available, using mock mode:', e?.message || e);
      this.neo4j = null;
      this.driverLoaded = true;
      this.useMockData = true;
      return null;
    }
  }

  /**
   * Health Monitoring Methods
   */

  async performHealthCheck() {
    try {
      const startTime = Date.now();

      if (this.useApiProxy) {
        const response = await fetch('/api/neo4j/health', {
          method: 'GET',
          headers: { 'Content-Type': 'application/json' }
        });

        if (!response.ok) {
          throw new Error(`API health check failed: ${response.status}`);
        }

        const data = await response.json();
        this.healthMetrics.isHealthy = data.healthy;
      } else if (this.driver && this.isConnected) {
        const session = this.driver.session();
        try {
          await session.run('RETURN 1 as healthCheck');
          this.healthMetrics.isHealthy = true;
          this.healthMetrics.consecutiveFailures = 0;
        } finally {
          await session.close();
        }
      } else {
        this.healthMetrics.isHealthy = false;
      }

      this.healthMetrics.lastHealthCheck = new Date();
      this.healthMetrics.connectionUptime = Date.now() - startTime;

      return {
        healthy: this.healthMetrics.isHealthy,
        uptime: this.healthMetrics.connectionUptime,
        lastCheck: this.healthMetrics.lastHealthCheck,
        consecutiveFailures: this.healthMetrics.consecutiveFailures
      };

    } catch (error) {
      this.healthMetrics.isHealthy = false;
      this.healthMetrics.consecutiveFailures++;
      this.healthMetrics.lastHealthCheck = new Date();

      console.warn('Health check failed:', error.message);

      return {
        healthy: false,
        error: error.message,
        consecutiveFailures: this.healthMetrics.consecutiveFailures
      };
    }
  }

  getMetrics() {
    return {
      queryStats: { ...this.queryStats },
      healthMetrics: { ...this.healthMetrics },
      rateLimiter: {
        currentRequests: this.rateLimiter.requests.length,
        maxRequests: this.rateLimiter.maxRequests,
        windowMs: this.rateLimiter.windowMs
      },
      cacheStats: {
        size: this.queryCache.size,
        timeout: this.cacheTimeout
      }
    };
  }

  async connect(config = null) {
    try {
      if (this.useApiProxy) {
        // Probe API health quickly; we won't establish driver
        const healthCheck = await this.performHealthCheck();
        this.isConnected = healthCheck.healthy;
        this.useMockData = !healthCheck.healthy;
        return {
          success: true,
          mockMode: !healthCheck.healthy,
          via: 'api',
          health: healthCheck
        };
      }

      const neo4j = await this.loadNeo4j();

      if (!neo4j) {
        console.log('Neo4j driver not available, using mock mode');
        this.useMockData = true;
        this.isConnected = false;
        return { success: true, mockMode: true };
      }

      const connectionConfig = config || this.connectionConfig;

      if (!connectionConfig.password) {
        console.warn('Neo4j password not provided, falling back to mock mode');
        this.useMockData = true;
        this.isConnected = false;
        return { success: true, mockMode: true };
      }

      // Normalize URI scheme based on page protocol, with optional override
      let uri = connectionConfig.uri || 'bolt://localhost:7687';
      // If running on http, prefer non-encrypted schemes unless forced via env
      const isHttpPage = typeof window !== 'undefined' && window.location && window.location.protocol === 'http:';
      const forceEncryption = (typeof import.meta !== 'undefined' && import.meta.env && import.meta.env.VITE_NEO4J_FORCE_ENCRYPTION === 'true');

      console.log('🔐 Neo4j connection config:', { isHttpPage, forceEncryption, originalUri: uri });

      if (isHttpPage && !forceEncryption) {
        uri = uri.replace(/^neo4j\+s\+ssc:|^neo4j\+s:|^bolt\+s\+ssc:|^bolt\+s:/, 'bolt://');
        uri = uri.replace(/^neo4j:$/, 'bolt://');
        console.log('🔓 Downgraded URI for HTTP page:', uri);
      }

      const authToken = neo4j.auth.basic(connectionConfig.username, connectionConfig.password);

      // Configure encryption based on page protocol and force flag
      const driverConfig = {};
      if (isHttpPage && !forceEncryption) {
        driverConfig.encrypted = 'ENCRYPTION_OFF';
        console.log('🔓 Encryption disabled for HTTP page');
      } else if (forceEncryption) {
        console.log('🔐 Encryption forced via VITE_NEO4J_FORCE_ENCRYPTION');
      }

      this.driver = neo4j.driver(uri, authToken, driverConfig);

      // Test the connection
      const session = this.driver.session({ database: connectionConfig.database });
      try {
        await session.run('RETURN 1 as test');
        this.isConnected = true;
        this.useMockData = false;
        console.log('Successfully connected to Neo4j database');
        return { success: true, mockMode: false };
      } finally {
        await session.close();
      }
    } catch (error) {
      console.warn('Failed to connect to Neo4j, falling back to mock mode:', error.message);
      this.isConnected = false;
      this.useMockData = true;
      this.lastError = error.message;

      if (this.driver) {
        try {
          await this.driver.close();
        } catch (closeError) {
          console.warn('Error closing driver:', closeError.message);
        }
        this.driver = null;
      }

      return { success: true, mockMode: true, error: error.message };
    }
  }

  async disconnect() {
    if (this.driver) {
      try {
        await this.driver.close();
        this.driver = null;
        this.isConnected = false;
        console.log('Disconnected from Neo4j database');
      } catch (error) {
        console.warn('Error disconnecting from Neo4j:', error.message);
      }
    }
  }
  /**
   * Safely converts a Neo4j integer to a JS number.
   * If the value is already a number, returns it as-is.
 */
  static convertNeo4jValue(value) {
    if (value === null || value === undefined) return value;
    // Neo4j integers have 'low' property and a 'toNumber' function
    if (typeof value.toNumber === 'function') {
      return value.toNumber();
    }
    return value; // already a JS number
  }

  /**
   * Schema Management Methods
   */

  async refreshSchema() {
    try {
      const queries = {
        nodeLabels: 'CALL db.labels()',
        relationshipTypes: 'CALL db.relationshipTypes()',
        indexes: 'CALL db.indexes()',
        constraints: 'CALL db.constraints()'
      };

      const results = {};

      for (const [key, query] of Object.entries(queries)) {
        try {
          const result = await this.executeQuery(query, {}, { skipCache: true });
          results[key] = result.records;
        } catch (error) {
          console.warn(`Failed to fetch ${key}:`, error.message);
          results[key] = [];
        }
      }

      // Update schema cache
      this.schemaCache = {
        nodeLabels: results.nodeLabels.map(r => r.label || r[Object.keys(r)[0]]),
        relationshipTypes: results.relationshipTypes.map(r => r.relationshipType || r[Object.keys(r)[0]]),
        indexes: results.indexes,
        constraints: results.constraints,
        lastUpdated: new Date()
      };

      return this.schemaCache;

    } catch (error) {
      console.error('Failed to refresh schema:', error.message);
      throw error;
    }
  }

  getSchema() {
    return { ...this.schemaCache };
  }

  async validateSchema() {
    const schema = await this.refreshSchema();
    const issues = [];

    // Check for missing indexes on common properties
    const commonProperties = ['patientId', 'timestamp', 'id'];
    for (const prop of commonProperties) {
      const hasIndex = schema.indexes.some(idx =>
        idx.properties && idx.properties.includes(prop)
      );
      if (!hasIndex) {
        issues.push({
          type: 'missing_index',
          property: prop,
          severity: 'warning',
          message: `Consider adding an index on property '${prop}' for better performance`
        });
      }
    }

    return {
      valid: issues.length === 0,
      issues,
      schema
    };
  }

  async executeQuery(query, parameters = {}) {
    // Validate inputs
    this.validateQuery(query);
    const sanitizedParams = this.validateParameters(parameters);

    // Check rate limit
    this.checkRateLimit();

    this.queryStats.totalQueries++;
    const startTime = Date.now();

    try {
      if (this.useApiProxy) {
        const resp = await fetch('/api/neo4j', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            query,
            parameters: sanitizedParams,
            database: this.connectionConfig.database
          })
        });
        if (!resp.ok) throw new Error(`API error ${resp.status}`);
        const data = await resp.json();
        const rows = Array.isArray(data) ? data : (Array.isArray(data.records) ? data.records : []);
        return { records: rows, summary: data.summary };
      }
      if (this.useMockData || !this.isConnected) {
        return this.getMockQueryResult(query, sanitizedParams);
      }

      // Check cache first
      const cacheKey = JSON.stringify({ query, parameters });
      if (this.queryCache.has(cacheKey)) {
        const cached = this.queryCache.get(cacheKey);
        if (Date.now() - cached.timestamp < this.cacheTimeout) {
          this.queryStats.cacheHits++;
          return cached.result;
        }
      }

      const session = this.driver.session({ database: this.connectionConfig.database });
      try {
        const records = result.records.map(record => {
          const obj = {};
          record.keys.forEach((key, index) => {
            const value = record.get(index);
            obj[key] = Neo4jService.convertNeo4jValue(value); //replaced with converted neo4jvalue
          });
          return obj;
        });


        const queryResult = {
          records,
          summary: {
            queryType: result.summary.queryType,
            counters: result.summary.counters,
            resultAvailableAfter: Neo4jService.convertNeo4jValue(result.summary.resultAvailableAfter),
            resultConsumedAfter: Neo4jService.convertNeo4jValue(result.summary.resultConsumedAfter)

          }
        };

        // Cache the result
        this.queryCache.set(cacheKey, {
          result: queryResult,
          timestamp: Date.now()
        });

        return queryResult;
      } finally {
        await session.close();
      }
    } catch (error) {
      console.warn('Query execution failed, returning mock data:', error.message);
      this.lastError = error.message;
      return this.getMockQueryResult(query, parameters);
    } finally {
      const executionTime = Date.now() - startTime;
      this.queryStats.totalExecutionTime += executionTime;
      this.queryStats.avgExecutionTime = this.queryStats.totalExecutionTime / this.queryStats.totalQueries;

      // Track slow queries
      if (executionTime > 1000) {
        this.queryStats.slowQueries.push({
          query: query.substring(0, 100) + '...',
          executionTime,
          timestamp: new Date().toISOString()
        });
      }

      // Track recent queries
      this.queryStats.recentQueries.push({
        query: query.substring(0, 50) + '...',
        executionTime,
        timestamp: new Date().toISOString(),
        parameters: Object.keys(parameters)
      });

      // Keep only last 10 recent queries
      if (this.queryStats.recentQueries.length > 10) {
        this.queryStats.recentQueries = this.queryStats.recentQueries.slice(-10);
      }
    }
  }

  updateQueryStats(query, executionTime, recordCount, error = null) {
    this.queryStats.totalExecutionTime += executionTime;
    this.queryStats.avgExecutionTime = this.queryStats.totalExecutionTime / this.queryStats.totalQueries;

    // Track slow queries
    if (executionTime > 1000) {
      this.queryStats.slowQueries.push({
        query: query.substring(0, 100) + '...',
        executionTime,
        timestamp: new Date().toISOString(),
        error: error?.message
      });

      // Keep only last 20 slow queries
      if (this.queryStats.slowQueries.length > 20) {
        this.queryStats.slowQueries = this.queryStats.slowQueries.slice(0, 20);
      }
    }

    // Track recent queries
    this.queryStats.recentQueries.unshift({
      query: query.substring(0, 100) + '...',
      executionTime,
      timestamp: new Date().toISOString(),
      recordCount,
      error: error?.message
    });

    // Keep only last 10 queries
    if (this.queryStats.recentQueries.length > 10) {
      this.queryStats.recentQueries = this.queryStats.recentQueries.slice(0, 10);
    }
  }

  /**
   * Data Export/Import Utilities
   */

  async exportData(query, parameters = {}, format = 'json') {
    try {
      const result = await this.executeQuery(query, parameters);

      switch (format.toLowerCase()) {
        case 'json':
          return {
            data: result.records,
            metadata: {
              query,
              parameters,
              recordCount: result.records.length,
              exportedAt: new Date().toISOString()
            }
          };

        case 'csv':
          return this.convertToCSV(result.records);

        case 'cypher':
          return this.convertToCypher(result.records);

        default:
          throw new Error(`Unsupported export format: ${format}`);
      }
    } catch (error) {
      console.error('Export failed:', error.message);
      throw error;
    }
  }

  convertToCSV(records) {
    if (!records || records.length === 0) {
      return '';
    }

    const headers = Object.keys(records[0]);
    const csvRows = [headers.join(',')];

    for (const record of records) {
      const values = headers.map(header => {
        const value = record[header];
        if (value === null || value === undefined) return '';
        if (typeof value === 'string' && value.includes(',')) {
          return `"${value.replace(/"/g, '""')}"`;
        }
        return String(value);
      });
      csvRows.push(values.join(','));
    }

    return csvRows.join('\n');
  }

  convertToCypher(records) {
    // Generate CREATE statements for the data
    const statements = [];

    for (const record of records) {
      // This is a simplified conversion - would need more sophisticated logic
      // for real-world use cases
      const properties = Object.entries(record)
        .map(([key, value]) => `${key}: ${JSON.stringify(value)}`)
        .join(', ');

      statements.push(`CREATE (n {${properties}})`);
    }

    return statements.join(';\n') + ';';
  }

  getMockQueryResult(query, parameters) {
    // Return mock data based on query type
    const queryLower = query.toLowerCase();

    if (queryLower.includes('glucose') && queryLower.includes('reading')) {
      return {
        records: this.generateMockGlucoseData(parameters),
        summary: {
          queryType: 'r',
          counters: { nodesCreated: 0, relationshipsCreated: 0 },
          resultAvailableAfter: 5,
          resultConsumedAfter: 10
        }
      };
    }

    if (queryLower.includes('patient')) {
      return {
        records: this.generateMockPatientData(parameters),
        summary: {
          queryType: 'r',
          counters: { nodesCreated: 0, relationshipsCreated: 0 },
          resultAvailableAfter: 3,
          resultConsumedAfter: 8
        }
      };
    }

    // Default mock result
    return {
      records: [
        { result: 'Mock data - Neo4j driver not available', timestamp: new Date().toISOString() }
      ],
      summary: {
        queryType: 'r',
        counters: { nodesCreated: 0, relationshipsCreated: 0 },
        resultAvailableAfter: 1,
        resultConsumedAfter: 2
      }
    };
  }

  generateMockGlucoseData(parameters) {
    const patientId = parameters.patientId || 'MOCK_PATIENT_001';
    const days = parameters.days || 30;
    const limit = parameters.limit || 100;

    const data = [];
    const now = new Date();

    for (let i = 0; i < Math.min(limit, days * 4); i++) {
      const timestamp = new Date(now - i * 6 * 60 * 60 * 1000); // Every 6 hours
      const baseGlucose = 120 + Math.sin(i * 0.1) * 30; // Simulate daily patterns
      const noise = (Math.random() - 0.5) * 40;
      const glucose = Math.max(70, Math.min(300, baseGlucose + noise));

      data.push({
        timestamp: timestamp.toISOString(),
        glucose: Math.round(glucose),
        patientId,
        hour: timestamp.getHours()
      });
    }

    return data;
  }

  generateMockPatientData(parameters) {
    const patientId = parameters.patientId || 'MOCK_PATIENT_001';

    return [{
      patientId,
      patientName: 'John Doe (Mock)',
      age: 45,
      primaryCondition: 'Type 2 Diabetes',
      diagnosisDate: '2020-01-15',
      avgGlucoseLast7Days: 145.5,
      readingsLast7Days: 28,
      conditions: ['Type 2 Diabetes', 'Hypertension'],
      currentMedications: ['Metformin', 'Lisinopril']
    }];
  }

  getPerformanceStats() {
    return {
      ...this.queryStats,
      isConnected: this.isConnected,
      useMockData: this.useMockData,
      lastError: this.lastError,
      cacheSize: this.queryCache.size,
      connectionStatus: this.isConnected ? 'Connected' : (this.useMockData ? 'Mock Mode' : 'Disconnected')
    };
  }

  clearPerformanceStats() {
    this.queryStats = {
      totalQueries: 0,
      totalExecutionTime: 0,
      cacheHits: 0,
      avgExecutionTime: 0,
      slowQueries: [],
      recentQueries: []
    };
    this.queryCache.clear();
    console.log('Performance stats cleared');
  }

  static getCommonQueries() {
    return {
      // === GLUCOSE & AGP QUERIES ===
      glucose_stats: {
        name: "Glucose Statistics",
        description: "Calculate comprehensive glucose statistics for a patient",
        category: "Glucose Analysis",
        query: `
          MATCH (p:Patient {patientId: $patientId})-[:HAD_READING]->(g:GlucoseReading)
          RETURN
            min(g.glucose) AS minGlucose,
            max(g.glucose) AS maxGlucose,
            avg(g.glucose) AS avgGlucose,
            stdev(g.glucose) AS stdDevGlucose,
            count(g) AS numberOfReadings,
            p.name AS patientName,
            p.condition AS patientCondition
        `,
        parameters: ['patientId']
      },
      glucose_over_time: {
        name: "Glucose Over Time (AGP)",
        description: "Get glucose readings over time for AGP visualization - Primary AGP Query",
        category: "Glucose Analysis",
        query: `
          MATCH (p:Patient {patientId: $patientId})-[:HAD_READING]->(g:GlucoseReading)
          WHERE g.timestamp >= datetime() - duration('P' + toString(coalesce($days, 30)) + 'D')
          RETURN g.timestamp, g.glucose
          ORDER BY g.timestamp
          LIMIT toInteger(coalesce($limit, 1000))
        `,
        parameters: ['patientId', 'days', 'limit']
      },
      glucose_patterns: {
        name: "Daily Glucose Patterns",
        description: "Analyze glucose patterns by time of day for AGP insights",
        category: "Glucose Analysis",
        query: `
          MATCH (p:Patient {patientId: $patientId})-[:HAD_READING]->(g:GlucoseReading)
          WHERE g.timestamp >= datetime() - duration('P30D')
          WITH g, g.timestamp.hour AS hour
          RETURN
            hour,
            avg(g.glucose) AS avgGlucose,
            min(g.glucose) AS minGlucose,
            max(g.glucose) AS maxGlucose,
            count(g) AS readingCount,
            percentileDisc(g.glucose, 0.25) AS q25,
            percentileDisc(g.glucose, 0.5) AS median,
            percentileDisc(g.glucose, 0.75) AS q75
          ORDER BY hour
        `,
        parameters: ['patientId']
      },
      time_in_range: {
        name: "Time in Range Analysis",
        description: "Calculate time spent in different glucose ranges",
        category: "Glucose Analysis",
        query: `
          MATCH (p:Patient {patientId: $patientId})-[:HAD_READING]->(g:GlucoseReading)
          WHERE g.timestamp >= datetime() - duration('P' + toString(coalesce($days, 30)) + 'D')
          WITH count(g) AS totalReadings
          MATCH (p:Patient {patientId: $patientId})-[:HAD_READING]->(g:GlucoseReading)
          WHERE g.timestamp >= datetime() - duration('P' + toString(coalesce($days, 30)) + 'D')
          RETURN
            totalReadings,
            count(CASE WHEN g.glucose < 54 THEN 1 END) AS veryLow,
            count(CASE WHEN g.glucose >= 54 AND g.glucose < 70 THEN 1 END) AS low,
            count(CASE WHEN g.glucose >= 70 AND g.glucose <= 180 THEN 1 END) AS target,
            count(CASE WHEN g.glucose > 180 AND g.glucose <= 250 THEN 1 END) AS high,
            count(CASE WHEN g.glucose > 250 THEN 1 END) AS veryHigh,
            round((count(CASE WHEN g.glucose >= 70 AND g.glucose <= 180 THEN 1 END) * 100.0) / totalReadings, 1) AS tirPercentage
        `,
        parameters: ['patientId', 'days']
      },
      glucose_variability: {
        name: "Glucose Variability Metrics",
        description: "Calculate coefficient of variation and other variability metrics",
        category: "Glucose Analysis",
        query: `
          MATCH (p:Patient {patientId: $patientId})-[:HAD_READING]->(g:GlucoseReading)
          WHERE g.timestamp >= datetime() - duration('P' + toString(coalesce($days, 30)) + 'D')
          RETURN
            avg(g.glucose) AS meanGlucose,
            stdev(g.glucose) AS stdDev,
            round((stdev(g.glucose) / avg(g.glucose)) * 100, 1) AS coefficientOfVariation,
            max(g.glucose) - min(g.glucose) AS glucoseRange,
            count(g) AS totalReadings,
            p.name AS patientName
        `,
        parameters: ['patientId', 'days']
      },
      hypoglycemic_events: {
        name: "Hypoglycemic Events",
        description: "Find patterns in low glucose events for safety analysis",
        category: "Glucose Analysis",
        query: `
          MATCH (p:Patient {patientId: $patientId})-[:HAD_READING]->(g:GlucoseReading)
          WHERE g.glucose < 70 AND g.timestamp >= datetime() - duration('P' + toString(coalesce($days, 30)) + 'D')
          RETURN
            g.timestamp,
            g.glucose,
            g.timestamp.hour AS hourOfDay,
            CASE
              WHEN g.glucose < 54 THEN 'Severe'
              WHEN g.glucose < 63 THEN 'Moderate'
              ELSE 'Mild'
            END AS severity
          ORDER BY g.timestamp DESC
          LIMIT toInteger(coalesce($limit, 100))
        `,
        parameters: ['patientId', 'days', 'limit']
      },

      // === PATIENT & CLINICAL QUERIES ===
      patient_overview: {
        name: "Patient Overview",
        description: "Get comprehensive patient information and recent metrics",
        category: "Patient Care",
        query: `
          MATCH (p:Patient {patientId: $patientId})
          OPTIONAL MATCH (p)-[:HAD_READING]->(g:GlucoseReading)
          WHERE g.timestamp >= datetime() - duration('P7D')
          RETURN
            p.name AS patientName,
            p.age AS age,
            p.condition AS primaryCondition,
            COALESCE(p.diagnosisDate, p.diagnosis_date) AS diagnosisDate,
            avg(COALESCE(g.value, g.glucose)) AS avgGlucoseLast7Days,
            count(g) AS readingsLast7Days,
            [] AS conditions,
            [] AS currentMedications
        `,
        parameters: ['patientId']
      },
      medication_effectiveness: {
        name: "Medication Effectiveness",
        description: "Analyze glucose control before and after medication changes",
        category: "Clinical Analysis",
        query: `
          MATCH (p:Patient {patientId: $patientId})-[:PRESCRIBED]->(m:Medication)
          OPTIONAL MATCH (p)-[:HAD_READING]->(g1:GlucoseReading)
          WHERE g1.timestamp >= m.startDate - duration('P7D')
            AND g1.timestamp < m.startDate
          OPTIONAL MATCH (p)-[:HAD_READING]->(g2:GlucoseReading)
          WHERE g2.timestamp >= m.startDate
            AND g2.timestamp <= m.startDate + duration('P7D')
          RETURN
            m.name AS medication,
            m.startDate AS startDate,
            avg(g1.glucose) AS avgGlucoseBeforeMed,
            avg(g2.glucose) AS avgGlucoseAfterMed,
            count(g1) AS readingsBeforeMed,
            count(g2) AS readingsAfterMed,
            round(avg(g1.glucose) - avg(g2.glucose), 1) AS glucoseReduction
          ORDER BY m.startDate DESC
        `,
        parameters: ['patientId']
      },
      recent_appointments: {
        name: "Recent Appointments",
        description: "Get patient appointments with outcomes and glucose trends",
        category: "Patient Care",
        query: `
          MATCH (p:Patient {patientId: $patientId})-[:HAD_APPOINTMENT]->(a:Appointment)
          WHERE a.date >= datetime() - duration('P90D')
          OPTIONAL MATCH (p)-[:HAD_READING]->(g:GlucoseReading)
          WHERE g.timestamp >= a.date - duration('P7D')
            AND g.timestamp <= a.date + duration('P7D')
          RETURN
            a.date AS appointmentDate,
            a.type AS appointmentType,
            a.provider AS provider,
            a.outcome AS outcome,
            a.notes AS notes,
            avg(g.glucose) AS avgGlucoseAroundVisit,
            count(g) AS readingsAroundVisit
          ORDER BY a.date DESC
          LIMIT toInteger(coalesce($limit, 10))
        `,
        parameters: ['patientId', 'limit']
      },

      // === RESEARCH & ANALYTICS QUERIES ===
      population_glucose_trends: {
        name: "Population Glucose Trends",
        description: "Compare patient glucose control across patient cohorts",
        category: "Research Analytics",
        query: `
          MATCH (p:Patient)-[:HAD_READING]->(g:GlucoseReading)
          WHERE g.timestamp >= datetime() - duration('P30D')
          WITH p, avg(g.glucose) AS avgGlucose,
               count(CASE WHEN g.glucose >= 70 AND g.glucose <= 180 THEN 1 END) * 100.0 / count(g) AS tir
          RETURN
            p.condition AS conditionType,
            p.ageGroup AS ageGroup,
            avg(avgGlucose) AS populationAvgGlucose,
            avg(tir) AS populationAvgTIR,
            count(p) AS patientCount,
            stdev(avgGlucose) AS glucoseVariationAcrossPatients
          ORDER BY conditionType, ageGroup
        `,
        parameters: []
      },
      treatment_outcomes: {
        name: "Treatment Outcomes Analysis",
        description: "Analyze treatment effectiveness across different patient groups",
        category: "Research Analytics",
        query: `
          MATCH (p:Patient)-[:PRESCRIBED]->(m:Medication)
          MATCH (p)-[:HAD_READING]->(g:GlucoseReading)
          WHERE g.timestamp >= m.startDate AND g.timestamp <= m.startDate + duration('P90D')
          WITH p, m, avg(g.glucose) AS avgGlucose,
               count(CASE WHEN g.glucose >= 70 AND g.glucose <= 180 THEN 1 END) * 100.0 / count(g) AS tir
          RETURN
            m.name AS medicationName,
            m.class AS medicationClass,
            p.condition AS conditionType,
            avg(avgGlucose) AS treatmentAvgGlucose,
            avg(tir) AS treatmentAvgTIR,
            count(p) AS patientsOnTreatment,
            stdev(avgGlucose) AS treatmentVariation
          ORDER BY medicationClass, medicationName
        `,
        parameters: []
      },

      // === SYSTEM & DATA QUALITY QUERIES ===
      data_quality_check: {
        name: "Data Quality Assessment",
        description: "Check for missing data, outliers, and data consistency",
        category: "System Monitoring",
        query: `
          MATCH (p:Patient {patientId: $patientId})
          OPTIONAL MATCH (p)-[:HAD_READING]->(g:GlucoseReading)
          WHERE g.timestamp >= datetime() - duration('P30D')
          RETURN
            p.name AS patientName,
            count(g) AS totalReadings,
            count(CASE WHEN g.glucose IS NULL THEN 1 END) AS nullReadings,
            count(CASE WHEN g.glucose < 20 OR g.glucose > 400 THEN 1 END) AS outlierReadings,
            min(g.timestamp) AS firstReading,
            max(g.timestamp) AS lastReading,
            duration.between(min(g.timestamp), max(g.timestamp)).days AS daysCovered,
            round(count(g) * 1.0 / duration.between(min(g.timestamp), max(g.timestamp)).days, 1) AS readingsPerDay
        `,
        parameters: ['patientId']
      },
      system_overview: {
        name: "System Data Overview",
        description: "Get high-level system statistics and data health metrics",
        category: "System Monitoring",
        query: `
          MATCH (p:Patient)
          OPTIONAL MATCH (p)-[:HAD_READING]->(g:GlucoseReading)
          WHERE g.timestamp >= datetime() - duration('P30D')
          OPTIONAL MATCH ()-[:HAD_APPOINTMENT]->(a:Appointment)
          WHERE a.date >= datetime() - duration('P30D')
          RETURN
            count(DISTINCT p) AS totalPatients,
            count(g) AS totalGlucoseReadings,
            count(a) AS totalAppointments,
            avg(COALESCE(g.value, g.glucose)) AS systemAvgGlucose,
            min(g.timestamp) AS oldestReading,
            max(g.timestamp) AS newestReading
        `,
        parameters: []
      }
    };
  }

  // ... rest of existing methods remain the same ...
}

export default Neo4jService;
