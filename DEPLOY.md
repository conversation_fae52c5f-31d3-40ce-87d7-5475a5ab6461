# CI/CD Setup for Cloudflare Pages & Workers

This project uses a single, comprehensive GitHub Actions workflow for automated testing, building, and deployment to Cloudflare Pages with Cloudflare Workers support.

## Single Workflow: `ci-cd.yml`

**Triggers:**
- **Push to `main`/`develop`:** Full pipeline (build, test, deploy)
- **Pull Requests to `main`:** Build and test only (no deployment)
- **Version tags (`v*`):** Production deployment

### Jobs Overview:

#### 1. **`build-and-test`** (Always runs)
- Installs dependencies and runs ESLint
- Builds the application with environment variables (uses mock values for PRs)
- Validates build artifacts and uploads them for deployment
- **Runs on:** All pushes and pull requests

#### 2. **`validate-workers`** (Conditional)
- Validates Cloudflare Workers functions syntax
- Checks `wrangler.toml` configuration
- **Condition:** Only runs if `functions/**/*.js` files exist

#### 3. **`deploy`** (Conditional)
- Downloads build artifacts from the build job
- Deploys to Cloudflare Pages
- **Condition:** Only on push to `main` or version tags
- **Environment:** `production` for main, `staging` for other branches

#### 4. **`deploy-workers`** (Conditional)
- Deploys Cloudflare Workers functions
- **Condition:** Only on push to `main` AND if functions exist
- **Depends on:** All previous jobs
- **Environment:** `production`

## ✅ **Your Cloudflare Credentials**

Based on your API verification, here are your specific values:

### Cloudflare Configuration
```bash
CL0UDFLARE_API_TOKEN=your_generated_token_here
CLOUDFLARE_ACCOUNT_ID=609ff70dd8138d7919a441be739ca4c1
```

### Application Environment Variables (Optional)
```bash
VITE_NEO4J_URI          # Neo4j connection URI (e.g., bolt://localhost:7687)
VITE_NEO4J_USERNAME     # Neo4j username
VITE_NEO4J_PASSWORD     # Neo4j password
VITE_NEO4J_DATABASE     # Neo4j database name (default: neo4j)
VITE_OPENAI_API_KEY     # OpenAI API key for AI features

Security note: Never commit real secrets to the repository. Use .env locally (gitignored) and Secrets in CI/Cloudflare.
```

## Required GitHub Secrets

Add these secrets to your GitHub repository settings:

## Setup Instructions

### 1. Cloudflare Setup
1. Create a Cloudflare Pages project named `healthhub-research-platform`
2. **Connect to your GitHub repository**
3. **IMPORTANT - Build Settings Only:**
   ```
   Build command: npm run build
   Build output directory: dist
   Root directory: / (leave empty)
   ```
4. **DO NOT set a deploy command** - GitHub Actions handles deployment
5. Generate an API token with `Pages:Edit` permissions
6. Note your Account ID from the Cloudflare dashboard

### 2. GitHub Repository Setup
1. Go to your repository Settings → Secrets and variables → Actions
2. Add all required secrets listed above
3. Create a `production` environment for production deployments (optional)

### 3. Project Configuration
Your `wrangler.toml` is already configured with the correct Account ID.

### 4. Test Deployment
Push to `main` branch or create a pull request to trigger the CI/CD pipeline.

## Local Development with Workers

```bash
# Install Wrangler CLI
npm install -g wrangler

# Test workers locally
wrangler pages dev dist --port 8788

# Deploy manually
wrangler pages deploy dist --project-name=healthhub-research-platform
```

## Workflow Features

- **Single workflow file** - No conflicts or redundancy
- **Efficient artifact sharing** - Build once, deploy multiple times
- **Smart conditional deployment** - Only deploys when needed
- **Environment-aware** - Production vs staging environments
- **Comprehensive validation** - Build, lint, and Workers checks
- **Fallback environment variables** - Works without secrets for testing
- **Parallel job execution** - Faster CI/CD pipeline
- **Clear job dependencies** - Logical execution order## Troubleshooting

### Common Issues

#### 1. **Wrangler Deploy Error: "Workers-specific command in Pages project"**
**Error:** `wrangler deploy` fails with message about Pages vs Workers

**Solution:**
1. In Cloudflare Pages dashboard, **DO NOT** set a deploy command
2. Use only these build settings:
   ```
   Build command: npm run build
   Build output directory: dist
   ```
3. Remove any custom deploy commands from Pages settings
4. Let the GitHub Actions handle deployment via `cloudflare/pages-action`

#### 2. **Multiple Environments Warning**
**Warning:** "Multiple environments are defined in Wrangler configuration"

**Solution:** This is normal for Pages projects and can be ignored. The GitHub Actions specify the correct environment.

#### 3. **Build Failures**
- Check Node.js version compatibility (using 18.17.0)
- Verify environment variables are set correctly in GitHub Secrets
- Review build logs for dependency issues

## Monitoring

- **Build status:** Check GitHub Actions tab
- **Deployment logs:** Available in Cloudflare dashboard
- **Live site:** Your Cloudflare Pages URL
- **Worker endpoints:** `your-site.pages.dev/api/function-name`
