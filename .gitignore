# Ignore local wrangler state, but not wrangler.toml or /functions (needed for deployment)
.wrangler/
# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Dependencies
node_modules
.pnp
.pnp.js

# Build outputs
dist
dist-ssr
build
*.local

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage
*.lcov

# Ignore all markdown files
*.md

# But don't ignore README files and development docs
!README.md
!readme.md
!Readme.md
!README.MD
!DEVELOPMENT.md
!DEPLOY.md


# Cloudflare Pages
.cloudflare
# Do NOT ignore /functions so Pages Functions deploy correctly

# Cache
.npm
.eslintcache
.parcel-cache

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS generated files
Thumbs.db
ehthumbs.db
Desktop.ini

# Temporary folders
tmp/
temp/

# Optional REPL history
.node_repl_history

# Yarn
yarn-error.log
.yarn-integrity

# Storybook build outputs
storybook-static

# Data folder (contains large datasets and should not be versioned)
data/
