/**
 * Neo4j Schema Management Utility
 * Provides tools for managing database schema, indexes, and constraints
 */

export class SchemaManager {
  constructor(neo4jService) {
    this.service = neo4jService;
    this.schemaDefinitions = new Map();
  }

  /**
   * Define a schema for validation and management
   */
  defineSchema(name, definition) {
    this.schemaDefinitions.set(name, {
      ...definition,
      name,
      createdAt: new Date()
    });
  }

  /**
   * Get all defined schemas
   */
  getSchemaDefinitions() {
    return Array.from(this.schemaDefinitions.values());
  }

  /**
   * Create indexes for better query performance
   */
  async createIndexes(indexes) {
    const results = [];
    
    for (const index of indexes) {
      try {
        const query = this.buildIndexQuery(index);
        await this.service.executeQuery(query);
        results.push({
          success: true,
          index: index.name,
          query
        });
      } catch (error) {
        results.push({
          success: false,
          index: index.name,
          error: error.message
        });
      }
    }
    
    return results;
  }

  /**
   * Create constraints for data integrity
   */
  async createConstraints(constraints) {
    const results = [];
    
    for (const constraint of constraints) {
      try {
        const query = this.buildConstraintQuery(constraint);
        await this.service.executeQuery(query);
        results.push({
          success: true,
          constraint: constraint.name,
          query
        });
      } catch (error) {
        results.push({
          success: false,
          constraint: constraint.name,
          error: error.message
        });
      }
    }
    
    return results;
  }

  /**
   * Build index creation query
   */
  buildIndexQuery(index) {
    const { name, label, properties, type = 'BTREE' } = index;
    
    if (!name || !label || !properties || properties.length === 0) {
      throw new Error('Index must have name, label, and properties');
    }

    const propList = Array.isArray(properties) ? properties.join(', ') : properties;
    
    if (type === 'TEXT') {
      return `CREATE TEXT INDEX ${name} IF NOT EXISTS FOR (n:${label}) ON (${propList})`;
    } else if (type === 'FULLTEXT') {
      return `CREATE FULLTEXT INDEX ${name} IF NOT EXISTS FOR (n:${label}) ON EACH [${propList}]`;
    } else {
      return `CREATE INDEX ${name} IF NOT EXISTS FOR (n:${label}) ON (${propList})`;
    }
  }

  /**
   * Build constraint creation query
   */
  buildConstraintQuery(constraint) {
    const { name, label, properties, type } = constraint;
    
    if (!name || !label || !properties || !type) {
      throw new Error('Constraint must have name, label, properties, and type');
    }

    const propList = Array.isArray(properties) ? properties.join(', ') : properties;

    switch (type.toUpperCase()) {
      case 'UNIQUE':
        return `CREATE CONSTRAINT ${name} IF NOT EXISTS FOR (n:${label}) REQUIRE (${propList}) IS UNIQUE`;
      case 'NODE_KEY':
        return `CREATE CONSTRAINT ${name} IF NOT EXISTS FOR (n:${label}) REQUIRE (${propList}) IS NODE KEY`;
      case 'EXISTS':
        return `CREATE CONSTRAINT ${name} IF NOT EXISTS FOR (n:${label}) REQUIRE ${propList} IS NOT NULL`;
      default:
        throw new Error(`Unsupported constraint type: ${type}`);
    }
  }

  /**
   * Analyze current schema and suggest improvements
   */
  async analyzeSchema() {
    const analysis = {
      suggestions: [],
      warnings: [],
      errors: [],
      statistics: {}
    };

    try {
      // Get current schema
      const schema = await this.service.refreshSchema();
      
      // Analyze node counts
      const nodeStats = await this.getNodeStatistics();
      analysis.statistics.nodes = nodeStats;

      // Analyze relationship counts
      const relStats = await this.getRelationshipStatistics();
      analysis.statistics.relationships = relStats;

      // Check for missing indexes
      const indexSuggestions = this.suggestIndexes(nodeStats, schema.indexes);
      analysis.suggestions.push(...indexSuggestions);

      // Check for potential constraint violations
      const constraintWarnings = await this.checkConstraintViolations();
      analysis.warnings.push(...constraintWarnings);

      // Check for orphaned nodes
      const orphanedNodes = await this.findOrphanedNodes();
      if (orphanedNodes.length > 0) {
        analysis.warnings.push({
          type: 'orphaned_nodes',
          message: `Found ${orphanedNodes.length} orphaned nodes`,
          details: orphanedNodes
        });
      }

    } catch (error) {
      analysis.errors.push({
        type: 'analysis_error',
        message: error.message
      });
    }

    return analysis;
  }

  /**
   * Get node statistics for analysis
   */
  async getNodeStatistics() {
    const query = `
      CALL db.labels() YIELD label
      CALL {
        WITH label
        CALL apoc.cypher.run('MATCH (n:' + label + ') RETURN count(n) as count', {})
        YIELD value
        RETURN label, value.count as count
      }
      RETURN label, count
      ORDER BY count DESC
    `;

    try {
      const result = await this.service.executeQuery(query);
      return result.records.map(record => ({
        label: record.label,
        count: record.count
      }));
    } catch (error) {
      // Fallback for databases without APOC
      const labels = await this.service.executeQuery('CALL db.labels()');
      const stats = [];
      
      for (const labelRecord of labels.records) {
        const label = labelRecord.label || labelRecord[Object.keys(labelRecord)[0]];
        try {
          const countResult = await this.service.executeQuery(
            `MATCH (n:${label}) RETURN count(n) as count`
          );
          stats.push({
            label,
            count: countResult.records[0].count
          });
        } catch (err) {
          console.warn(`Failed to count nodes for label ${label}:`, err.message);
        }
      }
      
      return stats.sort((a, b) => b.count - a.count);
    }
  }

  /**
   * Get relationship statistics
   */
  async getRelationshipStatistics() {
    const query = `
      CALL db.relationshipTypes() YIELD relationshipType
      CALL {
        WITH relationshipType
        CALL apoc.cypher.run('MATCH ()-[r:' + relationshipType + ']-() RETURN count(r) as count', {})
        YIELD value
        RETURN relationshipType, value.count as count
      }
      RETURN relationshipType, count
      ORDER BY count DESC
    `;

    try {
      const result = await this.service.executeQuery(query);
      return result.records.map(record => ({
        type: record.relationshipType,
        count: record.count
      }));
    } catch (error) {
      // Fallback for databases without APOC
      const types = await this.service.executeQuery('CALL db.relationshipTypes()');
      const stats = [];
      
      for (const typeRecord of types.records) {
        const type = typeRecord.relationshipType || typeRecord[Object.keys(typeRecord)[0]];
        try {
          const countResult = await this.service.executeQuery(
            `MATCH ()-[r:\`${type}\`]-() RETURN count(r) as count`
          );
          stats.push({
            type,
            count: countResult.records[0].count
          });
        } catch (err) {
          console.warn(`Failed to count relationships for type ${type}:`, err.message);
        }
      }
      
      return stats.sort((a, b) => b.count - a.count);
    }
  }

  /**
   * Suggest indexes based on node counts and existing indexes
   */
  suggestIndexes(nodeStats, existingIndexes) {
    const suggestions = [];
    const existingIndexProps = new Set(
      existingIndexes.map(idx => `${idx.labelsOrTypes}:${idx.properties?.join(',')}`)
    );

    // Suggest indexes for high-volume node labels
    const highVolumeLabels = nodeStats.filter(stat => stat.count > 1000);
    
    for (const stat of highVolumeLabels) {
      const commonProps = ['id', 'patientId', 'timestamp', 'name', 'email'];
      
      for (const prop of commonProps) {
        const indexKey = `${stat.label}:${prop}`;
        if (!existingIndexProps.has(indexKey)) {
          suggestions.push({
            type: 'index_suggestion',
            priority: 'high',
            message: `Consider adding index on ${stat.label}.${prop}`,
            query: `CREATE INDEX ${stat.label.toLowerCase()}_${prop}_idx IF NOT EXISTS FOR (n:${stat.label}) ON (n.${prop})`
          });
        }
      }
    }

    return suggestions;
  }

  /**
   * Check for potential constraint violations
   */
  async checkConstraintViolations() {
    const warnings = [];

    try {
      // Check for duplicate IDs
      const duplicateIds = await this.service.executeQuery(`
        MATCH (n)
        WHERE n.id IS NOT NULL
        WITH n.id as id, count(n) as count
        WHERE count > 1
        RETURN id, count
        LIMIT 10
      `);

      if (duplicateIds.records.length > 0) {
        warnings.push({
          type: 'duplicate_ids',
          message: 'Found nodes with duplicate IDs',
          details: duplicateIds.records
        });
      }

      // Check for missing required properties
      const missingProps = await this.service.executeQuery(`
        MATCH (p:Patient)
        WHERE p.patientId IS NULL OR p.name IS NULL
        RETURN count(p) as count
      `);

      if (missingProps.records[0]?.count > 0) {
        warnings.push({
          type: 'missing_properties',
          message: `Found ${missingProps.records[0].count} patients with missing required properties`
        });
      }

    } catch (error) {
      console.warn('Failed to check constraint violations:', error.message);
    }

    return warnings;
  }

  /**
   * Find orphaned nodes (nodes with no relationships)
   */
  async findOrphanedNodes() {
    try {
      const result = await this.service.executeQuery(`
        MATCH (n)
        WHERE NOT (n)--()
        RETURN labels(n) as labels, count(n) as count
        ORDER BY count DESC
        LIMIT 10
      `);

      return result.records.map(record => ({
        labels: record.labels,
        count: record.count
      }));
    } catch (error) {
      console.warn('Failed to find orphaned nodes:', error.message);
      return [];
    }
  }

  /**
   * Generate migration script for schema changes
   */
  generateMigrationScript(changes) {
    const script = [];
    script.push('// Neo4j Schema Migration Script');
    script.push(`// Generated on ${new Date().toISOString()}`);
    script.push('');

    if (changes.indexes) {
      script.push('// Create Indexes');
      changes.indexes.forEach(index => {
        script.push(this.buildIndexQuery(index) + ';');
      });
      script.push('');
    }

    if (changes.constraints) {
      script.push('// Create Constraints');
      changes.constraints.forEach(constraint => {
        script.push(this.buildConstraintQuery(constraint) + ';');
      });
      script.push('');
    }

    if (changes.cleanup) {
      script.push('// Cleanup Operations');
      changes.cleanup.forEach(query => {
        script.push(query + ';');
      });
    }

    return script.join('\n');
  }
}

export default SchemaManager;
