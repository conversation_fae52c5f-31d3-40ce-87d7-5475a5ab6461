#!/usr/bin/env node

/**
 * Test Local Development Environment
 * 
 * This script tests that the local development environment is working correctly
 */

import { spawn } from 'child_process';
import fetch from 'node-fetch';

const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

async function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function testApiServer() {
  log('\n🧪 Testing API Server', 'bright');
  
  // Start the API server
  logInfo('Starting Wrangler dev server...');
  const wranglerProcess = spawn('npx', ['wrangler', 'pages', 'dev', '--port=8787', '--local'], {
    stdio: 'pipe',
    detached: false
  });

  let serverReady = false;
  let serverOutput = '';

  wranglerProcess.stdout.on('data', (data) => {
    const output = data.toString();
    serverOutput += output;
    if (output.includes('Ready on http://localhost:8787')) {
      serverReady = true;
    }
  });

  wranglerProcess.stderr.on('data', (data) => {
    serverOutput += data.toString();
  });

  // Wait for server to start
  let attempts = 0;
  while (!serverReady && attempts < 30) {
    await sleep(1000);
    attempts++;
  }

  if (!serverReady) {
    logError('API server failed to start within 30 seconds');
    wranglerProcess.kill();
    return false;
  }

  logSuccess('API server started successfully');

  // Test health endpoint
  try {
    logInfo('Testing health endpoint...');
    const response = await fetch('http://localhost:8787/api/health', {
      timeout: 5000
    });

    if (response.ok) {
      const health = await response.json();
      logSuccess(`Health check passed: ${health.status}`);
      
      if (health.features?.neo4j?.enabled) {
        logSuccess('Neo4j integration is enabled');
      } else {
        log('⚠️  Neo4j integration is not enabled', 'yellow');
      }

      if (health.features?.ai?.enabled) {
        logSuccess('AI integration is enabled');
      } else {
        log('⚠️  AI integration is not enabled', 'yellow');
      }
    } else {
      logError(`Health check failed: ${response.status} ${response.statusText}`);
      wranglerProcess.kill();
      return false;
    }
  } catch (error) {
    logError(`Health check request failed: ${error.message}`);
    wranglerProcess.kill();
    return false;
  }

  // Test AI endpoint
  try {
    logInfo('Testing AI endpoint...');
    const aiResponse = await fetch('http://localhost:8787/api/ai', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        action: 'health-check'
      }),
      timeout: 10000
    });

    if (aiResponse.ok) {
      const aiHealth = await aiResponse.json();
      logSuccess('AI endpoint is responding');
      
      if (aiHealth.ai?.enabled) {
        logSuccess('AI service is properly configured');
      }
    } else {
      log(`⚠️  AI endpoint returned: ${aiResponse.status}`, 'yellow');
    }
  } catch (error) {
    log(`⚠️  AI endpoint test failed: ${error.message}`, 'yellow');
  }

  // Test Neo4j endpoint
  try {
    logInfo('Testing Neo4j endpoint...');
    const neo4jResponse = await fetch('http://localhost:8787/api/neo4j', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        query: 'RETURN "test" as message',
        parameters: {}
      }),
      timeout: 10000
    });

    if (neo4jResponse.ok) {
      const neo4jResult = await neo4jResponse.json();
      logSuccess('Neo4j endpoint is responding');
      
      if (neo4jResult.records && neo4jResult.records.length > 0) {
        logSuccess('Neo4j database connection is working');
      }
    } else {
      log(`⚠️  Neo4j endpoint returned: ${neo4jResponse.status}`, 'yellow');
    }
  } catch (error) {
    log(`⚠️  Neo4j endpoint test failed: ${error.message}`, 'yellow');
  }

  // Clean up
  wranglerProcess.kill();
  logSuccess('API server tests completed');
  return true;
}

async function testFrontendBuild() {
  log('\n🏗️  Testing Frontend Build', 'bright');
  
  return new Promise((resolve) => {
    logInfo('Running Vite build...');
    const buildProcess = spawn('npm', ['run', 'build'], {
      stdio: 'pipe'
    });

    let buildOutput = '';
    buildProcess.stdout.on('data', (data) => {
      buildOutput += data.toString();
    });

    buildProcess.stderr.on('data', (data) => {
      buildOutput += data.toString();
    });

    buildProcess.on('close', (code) => {
      if (code === 0) {
        logSuccess('Frontend build completed successfully');
        resolve(true);
      } else {
        logError('Frontend build failed');
        console.log(buildOutput);
        resolve(false);
      }
    });
  });
}

async function main() {
  log('🧪 Testing Local Development Environment', 'bright');
  log('This will test the API server and frontend build\n', 'blue');

  try {
    // Test API server
    const apiSuccess = await testApiServer();
    
    // Test frontend build
    const buildSuccess = await testFrontendBuild();

    if (apiSuccess && buildSuccess) {
      log('\n🎉 All tests passed!', 'green');
      log('Your local development environment is working correctly.', 'green');
      log('\nTo start development:', 'bright');
      log('  npm run dev:local  # Start both API and frontend', 'cyan');
      log('  npm run dev        # Start frontend only', 'cyan');
      log('  npm run dev:api    # Start API only', 'cyan');
    } else {
      log('\n❌ Some tests failed', 'red');
      log('Please check the output above and fix any issues.', 'red');
      process.exit(1);
    }
  } catch (error) {
    logError(`Test failed: ${error.message}`);
    process.exit(1);
  }
}

if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}
